import React from 'react';
import { Sr<PERSON>hart, ISrBarChartData } from '../../utils/sr-charts';
import { reportColors } from '../../utils/colorUtils';

// Test component to verify chart behavior with single day data
export const ChartTest: React.FC = () => {
  // Mock data for single day (reproduces the issue)
  const singleDayChartData: ISrBarChartData = {
    chart_type: 'bar_chart',
    report_type: 'emails_sent',
    report_label: 'Single Day Test - Total emails sent over time (including follow ups)',
    y_axis: [
      { name: 'Sent', color: reportColors.sentColor, data: [220] },
      { name: 'Bounced', color: reportColors.bouncedColor, data: [0] },
      { name: 'Opened', color: reportColors.openedColor, data: [51] },
      { name: 'Clicked', color: reportColors.clickColor, data: [0] },
      { name: 'Unsubscribed', color: reportColors.unsubscribeColor, data: [0] },
      { name: '<PERSON>lied', color: reportColors.repliedColor, data: [0] },
      { name: 'Positive', color: reportColors.positiveColor, data: [0] }
    ],
    x_axis: ['2023-08-24T00:00:00.000Z']
  };

  // Mock data for multiple days (normal behavior)
  const multipleDaysChartData: ISrBarChartData = {
    chart_type: 'bar_chart',
    report_type: 'emails_sent',
    report_label: 'Multiple Days Test - Total emails sent over time (including follow ups)',
    y_axis: [
      { name: 'Sent', color: reportColors.sentColor, data: [150, 180, 220] },
      { name: 'Bounced', color: reportColors.bouncedColor, data: [5, 3, 0] },
      { name: 'Opened', color: reportColors.openedColor, data: [35, 42, 51] },
      { name: 'Clicked', color: reportColors.clickColor, data: [8, 12, 0] },
      { name: 'Unsubscribed', color: reportColors.unsubscribeColor, data: [2, 1, 0] },
      { name: 'Replied', color: reportColors.repliedColor, data: [12, 15, 0] },
      { name: 'Positive', color: reportColors.positiveColor, data: [8, 10, 0] }
    ],
    x_axis: ['2023-08-22T00:00:00.000Z', '2023-08-23T00:00:00.000Z', '2023-08-24T00:00:00.000Z']
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-8">Chart Test - Single Day vs Multiple Days</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Single Day Chart (Should have narrow bar and single date label)</h2>
        <div className="border border-gray-300 rounded-lg p-4">
          <SrChart chart_data={singleDayChartData} />
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Multiple Days Chart (Normal behavior)</h2>
        <div className="border border-gray-300 rounded-lg p-4">
          <SrChart chart_data={multipleDaysChartData} />
        </div>
      </div>
    </div>
  );
};

export default ChartTest;
