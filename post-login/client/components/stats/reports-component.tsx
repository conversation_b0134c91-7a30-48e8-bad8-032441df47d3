import * as React from 'react'
import * as _ from 'lodash';
import { Helmet } from 'react-helmet';
import { inject, observer } from 'mobx-react';
import { withRouter, RouteComponentProps, Route, Switch } from 'react-router-dom';

import { ProspectsStats } from './prospects-stats-component';
import { ListsStats } from './lists-stats-component';
import { TemplatesStats } from './templates-stats-component';
import { LinksStats } from './links-stats-component';
import { BestTimeOfDayStats } from './best-time-of-day-stats-component';
import { TeamStats } from './team-stats-component';
import { CategoryStats } from './category-stats-component';
import { TagStats } from './tags-stats-component';
import * as moment from 'moment-timezone';

import * as campaignsApi from '../../api/campaigns';
import * as tagsApi from '../../api/tags';
import * as statsApi from '../../api/stats';
// const MultiSelect = require("@khanacademy/react-multi-select").default;


import { SRRedirect } from '../helpers';
import { checkPermission } from '../../utils/permissions_related';
import { NotFoundPage } from '../../containers/not-found-page';
import { getReportsFilterCount } from '../../utils/filter-related';
import { ClickTrackingStats } from './click-tracking-component';
import { EmailsSentReport } from './emails-sent-report-component';
import { HotProspectsReports } from './hot-prospects-component';
// import { HelpPopup } from '../../components/common-components/help-popup';
import { ReplySentimentStats } from './reply-sentiment/reply_sentiment-stats-component';
import CampaignsSendStartReportComponent from './campaigns-send-report';
import { TasksReport } from './tasks-report-component';
import { CallReport } from './activity-overview/call-activity-report';
import { LinkedinActivityReport } from './activity-overview/linkedin-activity-report';
import { SenderEmailStats } from './sender-email-stats-component';
import { sender_email_rotation } from './reports-utils';
import ProspectStatsNew from './prospects-stats-new';
import ChartTest from './chart-test';
import { getCurrentTeamMembers, getTeamMemberOptions } from '../../utils/teams';
import { getFromAndTillCompareTimePeriod } from '../report-utils';
import { LogIn, Campaigns } from '@sr/shared-product-components';
import { ISRVerticalNavSection, ISRVerticalSectionNavItem, SRVerticalSectionNavV2 } from '../sr-vertical-section-nav';
import OverviewReport from './overview-report';
import { CallDispositionReport } from './activity-overview/call-disposition-report';
import { campaignStore } from '../../stores/CampaignStore';
import { getChannelsFromSteps } from '../../utils/campaign-utils';
import { Stats } from './stats';

const CSVLink = require("react-csv").CSVLink;

export function getDefaultPage(
  campaignSteps?: Campaigns.ICampaignStep[]
): ITabKeys {
  // If no campaign steps, default to email activity
  if (!campaignSteps || campaignSteps.length === 0) {
    return "email_activity";
  }

  // Use getChannelsFromSteps to get available channels in priority order
  const channelsWithSteps = getChannelsFromSteps(campaignSteps);
  
  // Return the first available channel's default report
  if (channelsWithSteps.includes('email')) {
    return "email_activity";
  }
  
  if (channelsWithSteps.includes('call')) {
    return "call_activity_report";
  }
  
  if (channelsWithSteps.includes('linkedin')) {
    return "linkedin_activity_report";
  }
  
  if (channelsWithSteps.includes('whatsapp')) {
    // Whatsapp doesn't have a specific report tab yet, default to prospect categories
    return "prospect_category";
  }
  
  if (channelsWithSteps.includes('sms')) {
    // SMS doesn't have a specific report tab yet, default to prospect categories
    return "prospect_category";
  }

  // If no steps at all, default to prospect categories (always available)
  return "prospect_category";
}

type ITabKeys = Stats.IReportType;
// interface IReportsTab {
//   key: ITabKeys,
//   displayName: string,
//   info: string,
//   path: string,
// }

export interface ITasksCompletedAndProspectsContactedCounts {
  prospects_contacted: number,
  linkedin_tasks_completed: number,
  call_tasks_completed: number,
  total_tasks_completed: number,
  email_sent_count: number
}

export interface ITasksCompletedAndProspectsContactedChangeInPercentage {
  prospects_contacted_change: number,
  linkedin_tasks_change: number,
  call_tasks_change: number,
  total_tasks_change: number,
  email_sent_change: number
}

interface IReportsProps extends RouteComponentProps<any> {
  logInStore?: LogIn.ILogInStore;
  campaignStore?: Campaigns.ICampaignStore;
  alertStore?: Alerts.IAlertStore;
  teamStore?: Teams.ITeamStore;
}


interface IReportsStates {
  reportsFilterObj: Stats.ICustomStatsFilter;
  fromDayDatePicker: Date;
  tillDayDatePicker: Date;

  campaignTags: Tags.ICampaignTag[];

  currentTab?: ITabKeys;
  isDownloadingReport: boolean;

  //prospect report related - start
  overall_stats: Stats.IStatsOverall;
  timewise_stats: Stats.ITimewiseReportStats[];
  timewise_stats_interval: "day" | "week";
  stepwise_stats: {}[],
  isLoadingOverall: boolean;
  isLoadingTimewise: boolean;
  isLoadingStepwise: boolean;
  //prospect report related - end

  isLoadingCampaignTags: boolean;

  // templates report related - start
  isLoadingTemplatesReport?: boolean;
  templateReports?: any;
  // templates report related - end

  // lists report related - start
  isLoadingListsReport?: boolean;
  listsReport?: any;
  // lists report related - end

  // Links report related - start
  linksReport?: any;
  isLoadingLinksReport?: boolean;
  // Links report related - end

  // Best time report related - start
  bestTimeReport?: any;
  isLoadingBestTimeReport?: boolean;
  // Best time report related - end

  // Teams report related - start
  isLoadingTeamsReport?: boolean;
  teamsReport?: any;
  // Teams report related - end

  // Category report related - start
  isLoadingProspectCategoryReport?: boolean;
  prospectCategoryReport?: Stats.ICategoryReportRow[];
  // Category report related - end

  // Sender Email related - start
  isLoadingSenderEmailReport?: boolean;
  senderEmailReport?: Stats.ISenderEmailReportRow[];
  // Sender Email related - end

  // Tags report related - start
  isLoadingTagsReport?: boolean;
  tagsReport?: Stats.ITagReportRow[];
  // Tags report related - end

  //Click tracking report - start
  clickTrackingReport?: any;
  isLoadingClickTrackingReport?: boolean;
  //Click tracking report - end


  downloadFileName?: string;

  callReport: Stats.IActivityResponseData[];
  linkedinReport: Stats.ILinkedinActivityResponseData[];

  reportsPage: number,
  searchTotalCount: number;
  searchMaxCount: number;
  searchPageSize: number;

  replySentimentReport: ReplySentiments.IReplySentimentStatsReport,
  campaignSendStartReport: Stats.ICampaignSendStartReport[],
  isLoadingCampaignSendStartReport: boolean,
  isLoadingReplySentimentReport: boolean
  campaignOptions: Stats.Options.CampaignOption[]
};



const bestTimeFilterOptions = [
  { id: 'open_rate', name: 'Open rate' },
  { id: 'reply_rate', name: 'Reply rate' },
  { id: 'total_open', name: 'Total opens' },
  { id: 'total_replied', name: 'Total replies' }
];



const initialOwnerId = 0;

class ReportsComponent extends React.Component<IReportsProps, IReportsStates> {

  constructor(props: IReportsProps) {
    super(props);

    this.state = {
      reportsFilterObj: this.getEmptyReportsFilterObj(),
      fromDayDatePicker: new Date,
      tillDayDatePicker: new Date,

      isDownloadingReport: false,

      campaignTags: [],
      campaignOptions: [],
      isLoadingCampaignTags: true,
      callReport: [],
      linkedinReport: [],

      //prospect reports related - start
      overall_stats: {} as Stats.IStatsOverall,
      timewise_stats: [],
      timewise_stats_interval: "day",
      stepwise_stats: [],

      isLoadingOverall: true,
      isLoadingTimewise: true,
      isLoadingStepwise: true,
      //prospect reports related - end

      // templates report related - start
      templateReports: [],
      isLoadingTemplatesReport: true,
      // templates report related - end


      // lists report related - start
      listsReport: [],
      isLoadingListsReport: true,
      // lists report related - end

      // Links report related - start
      linksReport: [],
      isLoadingLinksReport: true,
      // Links report related - end

      // Best time report related - start
      bestTimeReport: [],
      isLoadingBestTimeReport: true,
      // Best time report related - end

      // Teams report related - start
      teamsReport: [],
      isLoadingTeamsReport: true,
      // Teams report related - end

      // Teams report related - start
      prospectCategoryReport: [],
      isLoadingProspectCategoryReport: true,
      // Teams report related - end

      // Sender Email related - start
      senderEmailReport: [],
      isLoadingSenderEmailReport: true,
      // Sender Email related - end


      // Tags report related - start
      tagsReport: [],
      isLoadingTagsReport: true,
      // Tags report related - end

      //Click tracking report - start
      clickTrackingReport: [],
      isLoadingClickTrackingReport: true,
      //Click tracking report - end

      //Click tracking report - start
      campaignSendStartReport: [],
      isLoadingCampaignSendStartReport: true,
      //Click tracking report - end


      downloadFileName: '',

      reportsPage: 1,
      searchTotalCount: 0,
      searchMaxCount: 0,
      searchPageSize: 0,

      replySentimentReport: {} as ReplySentiments.IReplySentimentStatsReport,
      isLoadingReplySentimentReport: true
    }

    this.getAllTabs = this.getAllTabs.bind(this);
    this.updateReportsFilterObjState = this.updateReportsFilterObjState.bind(this);
    this.updateTemplatesReport = this.updateTemplatesReport.bind(this);
    this.updateListsReport = this.updateListsReport.bind(this);
    this.updateLinksReport = this.updateLinksReport.bind(this);
    this.updateCheckUsingKey = this.updateCheckUsingKey.bind(this);
    this.updateTeamsReport = this.updateTeamsReport.bind(this);
    this.updateProspectCategoryReport = this.updateProspectCategoryReport.bind(this);
    this.downloadReport = this.downloadReport.bind(this);
    this.updateTagsReport = this.updateTagsReport.bind(this);
    this.updateClickTrackingReport = this.updateClickTrackingReport.bind(this);
    this.onPageChange = this.onPageChange.bind(this);
    this.clickTrackingReport = this.clickTrackingReport.bind(this);
    this.replySentimentReport = this.replySentimentReport.bind(this);
    this.getCampaignTagsOptions = this.getCampaignTagsOptions.bind(this);
    this.getCampaignSendStartReport = this.getCampaignSendStartReport.bind(this);
    this.getCampaignTagsOptionsWithDefaultAll = this.getCampaignTagsOptionsWithDefaultAll.bind(this);

    this.downloadCampaignReportAPI = this.downloadCampaignReportAPI.bind(this);
    this.updateSenderEmailReport = this.updateSenderEmailReport.bind(this);
    this.downloadCallReport = this.downloadCallReport.bind(this);
    this.downloadLinkedinReport = this.downloadLinkedinReport.bind(this);
  }



  getActive(tab: string) {

    const pathName = this.props.location.pathname;
    const isActive = _.includes(pathName, `/${tab}`)

    return isActive
  }

  getAllTabs() {
    const campaignId = this.props.match.params.campaignId;
    const isCampaign = !!campaignId;
    // const isMultichannel = !!this.props.teamStore!.getMetaData.ff_multichannel || !!this.props.logInStore?.accountInfo.org.org_metadata.ff_multichannel
    // const calling_enabled = this.props.logInStore?.accountInfo.org.org_metadata.enable_native_calling? this.props.logInStore?.accountInfo.org.org_metadata.enable_native_calling : false

    const isAgencyAdminDashboardView = (this.props.logInStore!.getCurrentTeamId === 0);

    const campaignSendStatusTab: ISRVerticalSectionNavItem = {
      displayName: 'Campaign Error',
      isActive: this.getActive('campaign_send_start'),
      value: 'campaign_send_start',
      href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/campaign_send_start',
      statusForIcon: 'info'
    }

    if (isAgencyAdminDashboardView) {
      let tabs: ISRVerticalSectionNavItem[] = [];
      if (this.props.logInStore?.getFeatureFlagsObj.ff_campaign_send_start_report && !isCampaign) {
        tabs.push(campaignSendStatusTab);
      }
      return tabs;
    } else {

       // For campaign-specific reports, check which channels have steps
       let tabs: ISRVerticalSectionNavItem[] = [];
       
       if (isCampaign) {
         const campaignSteps = this.props.campaignStore?.getStepVariants;
         const channelsWithSteps = getChannelsFromSteps(campaignSteps);
         
         // Only include email-related tabs if campaign has email steps
         if (channelsWithSteps.includes('email')) {
           tabs.push(
             {
               displayName: 'Email activity',
               isActive: this.getActive('email_activity'),
               value: 'email_activity',
               href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/email_activity',
               statusForIcon: 'info',
             },
             {
               displayName: 'Templates',
               isActive: this.getActive('templates'),
               value: 'templates',
               statusForIcon: 'info',
               href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/templates',
             },
             {
               displayName: 'Lists',
               isActive: this.getActive('lists'),
               value: 'lists',
               statusForIcon: 'info',
               href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/lists',
             },
             {
               displayName: 'Link clicks',
               isActive: this.getActive('links'),
               value: 'link_clicks',
               statusForIcon: 'info',
               href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/links',
             },
             {
               displayName: 'Best time to send',
               isActive: this.getActive('besttime'),
               value: 'best_time_to_send',
               statusForIcon: 'info',
               href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/besttime',
             },
             {
               value: sender_email_rotation,
               displayName: 'Sender email',
               isActive: this.getActive(sender_email_rotation),
               statusForIcon: 'info',
               href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/' + sender_email_rotation,
             },
             {
               value: 'click_tracking',
               displayName: 'Click tracking',
               statusForIcon: 'info',
               isActive: this.getActive('click_tracking'),
               href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/click_tracking',
             }
           );
         }
       } else {
         // For global reports, include all email-related tabs
         tabs.push(
           {
             displayName: 'Email activity',
             isActive: this.getActive('email_activity'),
             value: 'email_activity',
             href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/email_activity',
             statusForIcon: 'info',
           },
           {
             displayName: 'Templates',
             isActive: this.getActive('templates'),
             value: 'templates',
             statusForIcon: 'info',
             href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/templates',
           },
           {
             displayName: 'Lists',
             isActive: this.getActive('lists'),
             value: 'lists',
             statusForIcon: 'info',
             href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/lists',
           },
           {
             displayName: 'Link clicks',
             isActive: this.getActive('links'),
             value: 'link_clicks',
             statusForIcon: 'info',
             href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/links',
           },
           {
             displayName: 'Best time to send',
             isActive: this.getActive('besttime'),
             value: 'best_time_to_send',
             statusForIcon: 'info',
             href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/besttime',
           },
           {
             value: sender_email_rotation,
             displayName: 'Sender email',
             isActive: this.getActive(sender_email_rotation),
             statusForIcon: 'info',
             href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/' + sender_email_rotation,
           },
           {
             value: 'click_tracking',
             displayName: 'Click tracking',
             statusForIcon: 'info',
             isActive: this.getActive('click_tracking'),
             href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/click_tracking',
           }
         );
       }
       
       // Always include prospect-related tabs (not channel-specific)
       tabs.push(
         {
           value: 'prospect_category',
           displayName: 'Prospect categories',
           isActive: this.getActive('prospect_categories'),
           statusForIcon: 'info',
           href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/prospect_categories',
         },
         {
           value: 'tags',
           displayName: 'Prospect tags',
           statusForIcon: 'info',
           isActive: this.getActive('tags'),
           href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/tags'
         }
       );

      const call_activity_report: ISRVerticalSectionNavItem ={
        displayName: "Call activity",
        isActive: this.getActive('call_activity_report'),
        value: 'call_activity_report',
        statusForIcon: 'info',
        href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/call_activity_report'
      }

      const call_disposition_report: ISRVerticalSectionNavItem = {
        displayName: "Call disposition",
        isActive: this.getActive('call_disposition_report'),
        value: 'call_disposition_report',
        statusForIcon: 'info',
        href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/call_disposition_report'
      }

      const linkedin_activity_report: ISRVerticalSectionNavItem  ={
        displayName: "Linkedin activity",
        isActive: this.getActive('linkedin_activity_report'),
        value: 'linkedin_activity_report',
        statusForIcon: 'info',
        href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/linkedin_activity_report'
      }

      // Add channel-specific tabs based on campaign steps
      if (isCampaign) {
        const campaignSteps = this.props.campaignStore?.getStepVariants;
        const channelsWithSteps = getChannelsFromSteps(campaignSteps);
        
        if (channelsWithSteps.includes('call')) {
          tabs.push(call_activity_report, call_disposition_report);
        }
        
        if (channelsWithSteps.includes('linkedin')) {
          tabs.push(linkedin_activity_report);
        }
      } else {
        // For global reports, show all multichannel tabs if not email-only campaign
        if (this.props.campaignStore?.getBasicInfo.settings?.campaign_type != "email") {
          tabs.push(call_activity_report, call_disposition_report, linkedin_activity_report);
        }
      }

      if (this.props.logInStore?.getFeatureFlagsObj.ff_campaign_send_start_report && !isCampaign) {
        tabs.push(campaignSendStatusTab);
      }

      // tabs.push({
      //   displayName: 'Tasks',
      //   isActive: this.getActive('tasks'),
      //   value: 'tasks',
      //   statusForIcon: 'info',
      //   href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/tasks'
      // })
      const isAdmin = this.props.logInStore!.getIsTeamAdmin;


      const teamTab: ISRVerticalSectionNavItem = {
        value: 'team',
        displayName: 'Team leaderboard',
        statusForIcon: 'info',
        isActive: this.getActive('team'),
        href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/team',
      }

      const emailsSentReportTab: ISRVerticalSectionNavItem = {
        value: 'emails_sent',
        displayName: 'Emails sent',
        isActive: this.getActive('emails_sent'),
        statusForIcon: 'info',
        href: '/dashboard/reports/emails_sent',
      }

      /*
        // Date : 25 Jun 2025 : Removed Hot Prospect from prospect reports
      
      const hotProspectsReportTab: ISRVerticalSectionNavItem = {
        value: 'hot_prospects',
        displayName: 'Hot prospects',
        isActive: this.getActive('hot_prospects'),
        statusForIcon: 'info',
        href: '/dashboard/reports/hot_prospects',
      }
      */

      const replySentimentTab: ISRVerticalSectionNavItem = {
        value: 'reply_sentiment',
        displayName: 'Reply sentiment',
        isActive: this.getActive('reply_sentiment'),
        statusForIcon: 'info',
        href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/reply_sentiment',
      }



      if (isAdmin && !isCampaign) {
        tabs.splice(1, 0, teamTab);
      }

      if (this.props.logInStore?.getAccountInfo.org.org_metadata.ff_emails_sent_report) {

        if (!isCampaign) {
          tabs.splice(1, 0, emailsSentReportTab);
        }

      }

      const show_reply_sentiment_report = this.props.teamStore?.getMetaData.reply_sentiment_report || false
      if (show_reply_sentiment_report) {
        tabs.push(replySentimentTab)
      }

      //always shown but with upgrade prompt
      // if (!isCampaign) {
      //   tabs.push(
      //     hotProspectsReportTab
      //   )
      // }

      if ( this.props.logInStore?.isSupportAccount) {
        tabs.splice(1, 0, {

          displayName: 'Prospects contacted',
          isActive: this.getActive('prospects'),
          value: 'prospects',
          href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/prospects',
          statusForIcon: 'info',
        })
      }
      if (!isCampaign && (this.props.logInStore?.isSupportAccount)) {
        tabs.splice(0, 0, {

          displayName: 'Overview',
          isActive: this.getActive('overview'),
          value: 'overview',
          href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/overview',
          statusForIcon: 'info',
        })
      }

      // Add chart test tab for development/testing
      if (process.env.NODE_ENV === 'development' || this.props.logInStore?.isSupportAccount) {
        tabs.push({
          displayName: 'Chart Test',
          isActive: this.getActive('chart_test'),
          value: 'chart_test',
          href: '/dashboard/' + (isCampaign ? ('campaigns/' + campaignId + '/') : '') + 'reports/chart_test',
          statusForIcon: 'info',
        });
      }

      return tabs;

    }



  }

  onClickTab(tab: ISRVerticalSectionNavItem) {
    const reportsFilterObj = this.state.reportsFilterObj;

    //Sending always All team members as 0 for intial case
    reportsFilterObj.account_ids = [0];
    // if (tab.key == 'team') {
    //   reportsFilterObj.account_ids = [0]
    // } else {
    //   const currentAid = this.props.logInStore!.getAccountInfo.user_id;
    //   reportsFilterObj.account_ids = [currentAid]
    // }
    // this.updateReportsFilterObjState(reportsFilterObj);


    this.setState({
      currentTab: tab.value as ITabKeys
    }, () => {
      this.updateReportsFilterObjState(reportsFilterObj);
    })

    this.props.history.push({
      pathname: tab.href,
      search: this.props.location.search // retain aid, tid
    });
  }

  //reports actions - related
  getEmptyReportsFilterObj() {
    const emptyFilterObj: Stats.ICustomStatsFilter = {
      time_period: 'alltime',
      compare_time_period: 'previous_period',
      campaign_ids: [0],//by default all campaigns
      campaign_tag_ids: [0], // by default all campaigns tags
      list_ids: [0],//by default all lists & no list
      account_ids: [initialOwnerId],//by default all members - TODO - if also visible in others views other than all view
      best_time_filter: 'open_rate',
      fromDay: new Date(),
      tillDay: new Date(),
      from: new Date().getTime(),//can remove
      till: new Date().getTime(),//can remove
      compare_from: new Date().getTime(),
      compare_till: new Date().getTime(),
      page: 1,
      reply_sentiment_uuids: ["0"]
    };

    return emptyFilterObj;
  }

  updateReportsFilterObjState(reportsFilterObj: Stats.ICustomStatsFilter) {
    console.log('updated reportsObj', reportsFilterObj);

    const tz = this.props.logInStore!.getAccountInfo.timezone

    let from = moment(reportsFilterObj.fromDay).tz(tz);
    const till = moment(reportsFilterObj.tillDay).tz(tz);

    // chekcing this condition when alltime selcted and campaign or Account created time is less then 6 days - TODO
    // because this prospects-contacted-report has timewise stats
    if (this.state.reportsFilterObj.time_period === 'alltime') {

      const diffDays = till.diff(from, 'days');
      if (diffDays < 6) {
        from = _.cloneDeep(till).subtract(6, 'days').startOf('day').utc();
      }
    }

    // NOTE: adding 1 hour to from / till times as geta=nd to DST issue
    //daylight_change
    //Commenting out on 19-jun-2024 because the reports were showing overshooting, we need to make this dynamic
    // reportsFilterObj.from = from.add(1, 'hour').toDate().getTime();
    // reportsFilterObj.till = till.add(1, 'hour').toDate().getTime();
    reportsFilterObj.from = from.toDate().getTime();
    reportsFilterObj.till = till.toDate().getTime();

    const [compareFrom, compareTill] = getFromAndTillCompareTimePeriod(reportsFilterObj.compare_time_period, reportsFilterObj.from, reportsFilterObj.till)
    reportsFilterObj.compare_from = compareFrom
    reportsFilterObj.compare_till = compareTill

    this.setState({ reportsFilterObj }, () => {
      this.debouncedFetchReports();
    });
  }

  debouncedFetchReports = _.debounce(this.fetchReport, 500)


  getStatsAccountOverall(filterObj: Stats.ICustomStatsFilter) {

    this.setState({ isLoadingOverall: true });
    return statsApi.getStatsAccountOverall(filterObj).then((res: any) => {
      this.setState({
        overall_stats: res.data.overall_stats,
        isLoadingOverall: false
      });
    });
  }

  getStatsAccountTimewise(filterObj: Stats.ICustomStatsFilter) {

    this.setState({ isLoadingTimewise: true });
    return statsApi.getStatsAccountTimewise(filterObj).then((res) => {
      this.setState({
        timewise_stats: res.data.timewise_stats,
        timewise_stats_interval: res.data.interval,
        isLoadingTimewise: false
      });
    });
  }

  getStatsCampaignOverall(filterObj: Stats.ICustomStatsFilter) {

    this.setState({ isLoadingOverall: true });
    return statsApi.getStatsCampaignOverall(filterObj).then((res: any) => {
      this.setState({
        overall_stats: res.data.overall_stats,
        isLoadingOverall: false
      });
    });
  }

  getStatsCampaignTimewise(filterObj: Stats.ICustomStatsFilter) {

    this.setState({ isLoadingTimewise: true });
    return statsApi.getStatsCampaignTimewise(filterObj).then((res: any) => {
      this.setState({
        timewise_stats: res.data.timewise_stats,
        timewise_stats_interval: res.data.interval,
        isLoadingTimewise: false
      });
    });
  }

  getStatsCampaignStepwise(filterObj: Stats.ICustomStatsFilter) {

    this.setState({ isLoadingStepwise: true });
    return statsApi.getStatsCampaignStepwise(filterObj).then((res: any) => {
      this.setState({
        stepwise_stats: res.data.stepwise_stats,
        isLoadingStepwise: false
      });
    });
  }

  // Prospects report related - end

  // Templates report related - start
  updateTemplatesReport(templateReports: any) { // used by sort functions
    this.setState({ templateReports: templateReports });
  }
  // Templates report related -end

  // Lists report related - start
  updateListsReport(listsReport: any) { // used by sort functions
    this.setState({ listsReport: listsReport });
  }
  // Lists report related - end

  // Links report related - start
  updateLinksReport(linksReport: any) { // used by sort functions
    this.setState({ linksReport: linksReport });
  }
  // Links report related - end

  // Best time report - start
  updateCheckUsingKey(e: any, data: any) {
    let reportsFilterObj = this.state.reportsFilterObj;
    reportsFilterObj.best_time_filter = data.value;
    this.updateReportsFilterObjState(reportsFilterObj);
  }
  // Best time report - end


  // team report related - start
  updateTeamsReport(teamsReport: any) {
    this.setState({ teamsReport: teamsReport });
  }
  // team report related - end

  // team report related - start
  updateProspectCategoryReport(prospectCategoryReport: any) {
    this.setState({ prospectCategoryReport: prospectCategoryReport });
  }

  updateSenderEmailReport(senderEmailReport: any) {
    this.setState({ senderEmailReport: senderEmailReport });
  }
  // team report related - end

  // tags report related - start
  updateTagsReport(tagsReport: any) {
    this.setState({ tagsReport: tagsReport });
  }
  // tags report related - end

  // team report related - start
  updateClickTrackingReport(clickTrackingReport: any) {
    this.setState({ clickTrackingReport: clickTrackingReport });
  }
  // team report related - end

  getFilename(type: string) {
    return '[' + new Date().getTime() + ']_smartreach_' + type + '_stats.csv';
  }

  downloadCampaignReportAPI(campaignIds: number[], from: number, till: number) {
    this.setState({ isDownloadingReport: true });

    campaignsApi.downloadCampaignReportV3(campaignIds, from, till).then((resp: any) => {
      this.setState({ isDownloadingReport: false });
    }).catch((err: any) => {
      this.setState({ isDownloadingReport: false });
    })

  }



  downloadCallReport(data: Stats.IActivityResponseData[]){
    this.setState({
      callReport: data
    }, () => {
      this.downloadReport()
    })


  }

  downloadLinkedinReport(data: Stats.ILinkedinActivityResponseData[]) {
    this.setState({
      linkedinReport: data
    }, () => {
      this.downloadReport()
    })


  }


  downloadReport() {
    const reportsFilterObj = this.state.reportsFilterObj;
    const campaignIds = reportsFilterObj.campaign_ids;
    const from = reportsFilterObj.fromDay.getTime();
    const till = reportsFilterObj.tillDay.getTime();

    const currentTab = this.state.currentTab;

    switch (currentTab) {

      case 'prospects':
        break;

      case 'linkedin_activity_report':
        if (this.state.linkedinReport.length > 0) {
          const fileName = this.getFilename(currentTab);
          this.setState({ downloadFileName: fileName }, () => {
            (document as any).getElementById("linkedin_activity_report_download").click();
          });
        } else {
          this.props.alertStore!.pushAlert({
            status: 'error',
            message: `No Data to download.`,
          });
        }
        break;

      case 'overview':
        break;

      case 'email_activity':
        // this.setState({ isDownloadingReport: true });
        // campaignsApi.downloadCampaignReportV3(campaignIds, from, till).then((resp: any) => {
        //   this.setState({ isDownloadingReport: false });
        // }).catch((err: any) => {
        //   this.setState({ isDownloadingReport: false });
        // })

        this.downloadCampaignReportAPI(campaignIds, from, till);

        break;

      case 'templates':
        if (this.state.templateReports.length > 0) {
          const fileName = this.getFilename(currentTab);
          this.setState({ downloadFileName: fileName }, () => {
            (document as any).getElementById("template_report_download").click();
          });
        } else {
          this.props.alertStore!.pushAlert({
            status: 'error',
            message: `No Data to download.`,
          });
        }
        break;

      case 'lists':
        if (this.state.listsReport.length > 0) {
          const fileName = this.getFilename(currentTab);
          this.setState({ downloadFileName: fileName }, () => {
            (document as any).getElementById("list_report_download").click();
          });
        } else {
          this.props.alertStore!.pushAlert({
            status: 'error',
            message: `No Data to download.`,
          });
        }
        break;

      case 'link_clicks':
        if (this.state.linksReport.length > 0) {
          const fileName = this.getFilename(currentTab);
          this.setState({ downloadFileName: fileName }, () => {
            (document as any).getElementById("links_report_download").click();
          });
        } else {
          this.props.alertStore!.pushAlert({
            status: 'error',
            message: `No Data to download.`,
          });
        }
        break;

      case 'best_time_to_send':
        break;

      case 'team':
        if (this.state.teamsReport.length > 0) {
          const fileName = this.getFilename(currentTab);
          this.setState({ downloadFileName: fileName }, () => {
            (document as any).getElementById("teams_report_download").click();
          });
        } else {
          this.props.alertStore!.pushAlert({
            status: 'error',
            message: `No Data to download.`,
          });
        }
        break;

      case 'call_activity_report':
        if (this.state.callReport.length > 0) {
          const fileName = this.getFilename(currentTab);
          this.setState({ downloadFileName: fileName }, () => {
            (document as any).getElementById("call_activity_report").click();
          });
        } else {
          this.props.alertStore!.pushAlert({
            status: 'error',
            message: `No Data to download.`,
          });
        }
        break;

      case 'prospect_category':
        if ((this.state.prospectCategoryReport || []).length > 0) {
          const fileName = this.getFilename(currentTab);
          this.setState({ downloadFileName: fileName }, () => {
            (document as any).getElementById("prospect_category_report_download").click();
          });
        } else {
          this.props.alertStore!.pushAlert({
            status: 'error',
            message: `No Data to download.`,
          });
        }
        break;


      case sender_email_rotation:
        if ((this.state.senderEmailReport || []).length > 0) {
          const fileName = this.getFilename(currentTab);
          this.setState({ downloadFileName: fileName }, () => {
            (document as any).getElementById("sender_email_report_download").click();
          });
        } else {
          this.props.alertStore!.pushAlert({
            status: 'error',
            message: `No Data to download.`,
          });
        }
        break;

      case 'click_tracking':
        if (this.state.clickTrackingReport.length > 0) {
          const fileName = this.getFilename(currentTab);
          this.setState({ downloadFileName: fileName }, () => {
            (document as any).getElementById("click_tracking_report_download").click();
          });
        } else {
          this.props.alertStore!.pushAlert({
            status: 'error',
            message: `No Data to download.`,
          });
        }
        break;

      case 'tags':
        if ((this.state.tagsReport || []).length > 0) {
          const fileName = this.getFilename(currentTab);
          this.setState({ downloadFileName: fileName }, () => {
            (document as any).getElementById("tag_report_download").click();
          });
        } else {
          this.props.alertStore!.pushAlert({
            status: 'error',
            message: `No Data to download.`,
          });
        }
        break;

      default:
        this.setState({ isDownloadingReport: false });
        this.props.alertStore!.pushAlert({
          status: 'error',
          message: `Download report not supported yet for current selected Report. Please contact support.`,
        });

        break;
    }
  }

  getCampaignSendStartReport() {
    this.setState({ isLoadingCampaignSendStartReport: true })

    statsApi.getCampaignSendStartReport()
      .then(response => {
        this.setState({ campaignSendStartReport: response.data.campaign_send_start_report, isLoadingCampaignSendStartReport: false })
      })
      .catch((err) => {
        console.error(err)
        this.setState({ isLoadingCampaignSendStartReport: false })
      })

  }

  fetchReport() {

    let filterObj = this.state.reportsFilterObj;

    const currentTab = this.state.currentTab;

    switch (currentTab) {

      case 'email_activity':
        if ((filterObj.campaign_ids.length > 0 &&
          filterObj.campaign_ids[0] !== 0
        ) ||
          (filterObj.campaign_tag_ids.length > 0 &&
            filterObj.campaign_tag_ids[0] !== 0
          )
        ) {
          Promise.all([
            this.getStatsCampaignOverall(filterObj),
            this.getStatsCampaignStepwise(filterObj),
            this.getStatsCampaignTimewise(filterObj)
          ])
        }
        else {
          Promise.all([
            this.getStatsAccountOverall(filterObj),
            this.getStatsCampaignStepwise(filterObj),
            this.getStatsAccountTimewise(filterObj)
          ])
        }

        break;

      case 'prospects':

        break;

      case 'linkedin_activity_report':
        break;

      case 'call_activity_report':
        break;

      case 'campaign_send_start':
        this.getCampaignSendStartReport();

        break;

      case 'templates':

        this.setState({ isLoadingTemplatesReport: true }, () => {

          statsApi.getTemplatesReports(filterObj)
            .then((res) => {
              this.setState({
                isLoadingTemplatesReport: false,
                templateReports: res.data.templates_stats
              });
            })
            .catch(() => {
              this.setState({ isLoadingTemplatesReport: false, templateReports: [] });
            })
        });

        break;

      case 'call_disposition_report':
          break;

      case 'lists':

        this.setState({ isLoadingListsReport: true }, () => {

          statsApi.getListsReports(filterObj)
            .then((res) => {
              this.setState({ isLoadingListsReport: false, listsReport: res.data.lists_stats });
            })
            .catch(() => {
              this.setState({ isLoadingListsReport: false, listsReport: [] });
            })
        });

        break;

      case 'link_clicks':

        this.setState({ isLoadingLinksReport: true }, () => {

          statsApi.getLinksReports(filterObj)
            .then((res) => {
              this.setState({ isLoadingLinksReport: false, linksReport: res.data.links_stats });
            })
            .catch(() => {
              this.setState({ isLoadingLinksReport: false, linksReport: [] });
            })
        });

        break;

      case 'best_time_to_send':

        this.setState({ isLoadingBestTimeReport: true }, () => {

          statsApi.getBestTimeOfDayReports(filterObj)
            .then((res) => {
              this.setState({ isLoadingBestTimeReport: false, bestTimeReport: res.data.best_time_of_day_stats });
            })
            .catch(() => {
              this.setState({ isLoadingBestTimeReport: false, bestTimeReport: [] });
            })
        });

        break;

      case 'team':

        this.setState({ isLoadingTeamsReport: true }, () => {
          statsApi.getTeamReports(filterObj)
            .then((res) => {
              this.setState({ isLoadingTeamsReport: false, teamsReport: res.data.teams_stats });
            })
            .catch(() => {
              this.setState({ isLoadingTeamsReport: false, teamsReport: [] });
            })
        });

        break;

      case 'prospect_category':

        this.setState({ isLoadingProspectCategoryReport: true }, () => {
          statsApi.getCategoryReports(filterObj)
            .then((res) => {
              this.setState({ isLoadingProspectCategoryReport: false, prospectCategoryReport: res.data.report });
            })
            .catch(() => {
              this.setState({ isLoadingProspectCategoryReport: false, prospectCategoryReport: [] });
            })
        });

        break;

      case sender_email_rotation:

        this.setState({ isLoadingProspectCategoryReport: true }, () => {
          statsApi.getSenderEmailReport(filterObj)
            .then((res) => {
              this.setState({ isLoadingSenderEmailReport: false, senderEmailReport: res.data.report });
            })
            .catch(() => {
              this.setState({ isLoadingSenderEmailReport: false, senderEmailReport: [] });
            })
        });

        break;

      case 'tags':

        this.setState({ isLoadingTagsReport: true }, () => {
          statsApi.getTagsReports(filterObj)
            .then((res) => {
              this.setState({ isLoadingTagsReport: false, tagsReport: res.data.report });
            })
            .catch(() => {
              this.setState({ isLoadingTagsReport: false, tagsReport: [] });
            })
        });

        break;

      case 'click_tracking':
        this.setState({ isLoadingClickTrackingReport: true }, () => {
          this.clickTrackingReport(filterObj);
        });

        break;

      case 'reply_sentiment':
        console.log("in reply_sentiment case ")
        this.setState({ isLoadingReplySentimentReport: true }, () => {
          this.replySentimentReport(filterObj);
        });

        break;


    }
  }
  replySentimentReport(updatedFilterObj: Stats.ICustomStatsFilter) {
    Promise.all([
      statsApi.replySentimentStats(updatedFilterObj),
      this.getStatsCampaignOverall(updatedFilterObj)
    ])
      .then(([res]) => {
        this.setState({
          isLoadingReplySentimentReport: false,
          replySentimentReport: res.data

        });
      })
      .catch(() => {
        this.setState({
          isLoadingClickTrackingReport: false,

        });
      })
  }
  clickTrackingReport(updatedFilterObj: Stats.ICustomStatsFilter) {
    statsApi.getClickTrackingReports(updatedFilterObj)
      .then((res) => {
        this.setState({
          isLoadingClickTrackingReport: false,
          clickTrackingReport: res.data.click_tracking_stats,
          searchTotalCount: res.data.total_count,
          searchMaxCount: res.data.max_count,
          searchPageSize: res.data.rows_per_page
        });
      })
      .catch(() => {
        this.setState({
          isLoadingClickTrackingReport: false,
          clickTrackingReport: [],
          searchTotalCount: 0,
          searchMaxCount: 0,
          searchPageSize: 0,
        });
      })
  }
  onPageChange(action: string, disabled: boolean) {
    const currPageNum = this.state.reportsPage || 1;
    const filterObj = this.state.reportsFilterObj

    if (action === 'prev' && !disabled && currPageNum > 1) {

      this.setState({ reportsPage: currPageNum - 1 }, () => {
        let updatedFilterObj = filterObj;
        updatedFilterObj.page = updatedFilterObj.page - 1;
        this.setState({ reportsFilterObj: updatedFilterObj }, () => {
          this.clickTrackingReport(updatedFilterObj);
        });

      })

    } else if (action === 'next' && !disabled) {
      this.setState({ reportsPage: currPageNum + 1 }, () => {
        let updatedFilterObj = filterObj;
        updatedFilterObj.page = updatedFilterObj.page + 1;
        this.setState({ reportsFilterObj: updatedFilterObj }, () => {
          this.clickTrackingReport(updatedFilterObj);
        });
      })
    }
  }



  componentDidMount() {
    console.log('reports component did mount');
    const isJumpermediaAccount = this.props.logInStore!.getAccountInfo.org.id === 2;
    const isInsideSpecificCampaign = !!this.props.match.params.campaignId;
    const campaignId = isInsideSpecificCampaign ? this.props.match.params.campaignId : 0;

    const defaultTimeval = isInsideSpecificCampaign ? 'month' : (isJumpermediaAccount ? 'week' : 'month');
    const timezone = this.props.logInStore!.getAccountInfo.timezone;
    let reportsFilterObj = this.state.reportsFilterObj;

    let defaultFromDay: moment.Moment = moment(this.props.logInStore!.getAccountInfo.created_at).tz(timezone).subtract(7, 'days').startOf('day').utc();
    if (defaultTimeval === 'week') {
      defaultFromDay = moment().tz(timezone).subtract(6, 'days').startOf('day').utc()
    } else if (defaultTimeval === 'month') {
      defaultFromDay = moment().tz(timezone).subtract(1, 'month').startOf('day').utc()
    }
    const isCampaign = !!this.props.match.params.campaignId;

    if (!isCampaign) {
      this.setState({ isLoadingOverall: true }, () => {
        campaignsApi.getBasicCampaignList()
          .then(res => {
            const campaignOptions: Stats.Options.CampaignOption[] = res.data.campaigns.map(c => {
              return ({
                id: c.id,
                name: c.name,
                created_at: c.created_at
              })
            })
            this.setState({ isLoadingOverall: false, campaignOptions })
          })
          .catch(err => {
            this.setState({ isLoadingOverall: false })
            console.error(err)
          })
      })
    }
    /*
    const defaultFromDay: moment.Moment = (defaultTimeval === 'week') ? moment().tz(timezone).subtract(6, 'days').startOf('day').utc()
      : ((defaultTimeval === 'alltime') ? moment(this.props.campaignStore!.getBasicInfo.created_at).tz(timezone).startOf('day').utc()
        : moment(this.props.logInStore!.getAccountInfo.created_at).tz(timezone).subtract(7, 'days').startOf('day').utc()
      );
      */
    const defaultTillDay = moment().tz(this.props.logInStore!.getAccountInfo.timezone).endOf('day').utc();

    reportsFilterObj.time_period = defaultTimeval;
    reportsFilterObj.fromDay = defaultFromDay.toDate();
    reportsFilterObj.tillDay = defaultTillDay.toDate();
    // reportsFilterObj.campaign_ids = isInsideSpecificCampaign ? [campaignId] : [0];
    reportsFilterObj.campaign_ids = isInsideSpecificCampaign ? [parseInt(campaignId.toString())] : [0];
    // reportsFilterObj.account_ids = [currentAid];

    // if (isInsideSpecificCampaign) {
    //   this.props.campaignStore!.updateTab('stats');
    // }

    //determining the sub tab
    const loc = this.props.location.pathname;

    const campaignSteps = campaignStore.getStepVariants;

    let currentTabFromLoc = !isInsideSpecificCampaign ? "overview" : getDefaultPage(campaignSteps);

    if (_.includes(loc, '/stats/prospects') || _.includes(loc, '/reports/prospects')) {

      currentTabFromLoc = 'prospects';

    } else if (_.includes(loc, '/stats/call_activity_report') || _.includes(loc, '/reports/call_activity_report')) {

      currentTabFromLoc = 'call_activity_report';

    } else if (_.includes(loc, '/stats/call_disposition_report') || _.includes(loc, '/reports/call_disposition_report')) {

      currentTabFromLoc = 'call_disposition_report';

    } else if (_.includes(loc, '/stats/templates') || _.includes(loc, '/reports/templates')) {

      currentTabFromLoc = 'templates';

    } else if (_.includes(loc, '/stats/overview') || _.includes(loc, '/reports/overview')) {

      currentTabFromLoc = 'overview';

    } else if (_.includes(loc, '/stats/email_activity') || _.includes(loc, '/reports/email_activity')) {

      currentTabFromLoc = 'email_activity';

    } else if (_.includes(loc, '/stats/lists') || _.includes(loc, '/reports/lists')) {

      currentTabFromLoc = 'lists';

    } else if (_.includes(loc, '/stats/links') || _.includes(loc, '/reports/links')) {

      currentTabFromLoc = 'link_clicks';

    } else if (_.includes(loc, '/stats/besttime') || _.includes(loc, '/reports/besttime')) {

      currentTabFromLoc = 'best_time_to_send';

    } else if (_.includes(loc, '/stats/team') || _.includes(loc, '/reports/team')) {

      reportsFilterObj.account_ids = [0];

      currentTabFromLoc = 'team';

    } else if (_.includes(loc, '/stats/' + sender_email_rotation) || _.includes(loc, '/reports/' + sender_email_rotation)) {

      reportsFilterObj.account_ids = [0];

      currentTabFromLoc = sender_email_rotation;

    } else if (_.includes(loc, '/stats/prospect_categories') || _.includes(loc, '/reports/prospect_categories')) {

      reportsFilterObj.account_ids = [0];

      currentTabFromLoc = 'prospect_category';

    } else if (_.includes(loc, '/stats/tags') || _.includes(loc, '/reports/tags')) {

      reportsFilterObj.account_ids = [0];

      currentTabFromLoc = 'tags';

    } else if (_.includes(loc, '/stats/click_tracking') || _.includes(loc, '/reports/click_tracking')) {

      currentTabFromLoc = 'click_tracking';

    } else if (_.includes(loc, '/reports/emails_sent')) {

      currentTabFromLoc = 'emails_sent';

    } else if (_.includes(loc, '/stats/reply_sentiment') || _.includes(loc, '/reports/reply_sentiment')) {
      currentTabFromLoc = "reply_sentiment"
    }
    else if (_.includes(loc, '/reports/campaign_send_start')) {
      currentTabFromLoc = "campaign_send_start"
    }

    this.setState({ currentTab: currentTabFromLoc ? currentTabFromLoc : undefined }, () => {
      this.updateReportsFilterObjState(reportsFilterObj);
    })


    // api calls
    const isAgencyAdminDashboardView = (this.props.logInStore!.getCurrentTeamId === 0);

    if (!!!campaignId && !isAgencyAdminDashboardView) {
      this.setState({ isLoadingCampaignTags: true })

      tagsApi.getCampaignTags()
        .then(campaignTags => {
          this.setState({ campaignTags: campaignTags.data.tags.filter(t => t.tag_id !== 0), isLoadingCampaignTags: false })
        })
        .catch(err => {
          console.error(err)
          this.setState({ isLoadingCampaignTags: false })
        })
    }

  }

  getCampaignTagsOptionsWithDefaultAll(): Tags.ICampaignTag[] {

    // const defaultOption: Tags.ICampaignTag = {
    //   tag_id: 0,
    //   tag: 'All Campaign Tags'
    // };

    return this.state.campaignTags

  }


  getCampaignTagsOptions() {

    return this.getCampaignTagsOptionsWithDefaultAll()
      .map((o) => {
        return { key: String(o.tag_id), value: o.tag_id, label: o.tag }
      })
  }

  getCategoryReportObjToDownload(catReport: Stats.ICategoryReportRow[]) {
    let obj: { category: string, count: number }[] = [];
    _.map(catReport, (catReportRow) => {
      const newRow = {
        category: catReportRow.prospect_category,
        count: catReportRow.total.prospects_contacted
      };
      obj.push(newRow);
    })
    return obj;
  }

  getSenderEmailReportObjToDownload(catReport: Stats.ISenderEmailReportRow[]) {
    let obj: { email: string, count: number }[] = [];
    _.map(catReport, (catReportRow) => {
      const newRow = {
        email: catReportRow.sender_email,
        count: catReportRow.total.prospects_contacted
      };
      obj.push(newRow);
    })
    return obj;
  }

  getTagReportObjToDownload(tagReport: Stats.ITagReportRow[]) {
    let obj: { tag: string, count: number }[] = [];
    _.map(tagReport, (tagReportRow) => {
      const newRow = {
        tag: tagReportRow.tag,
        count: tagReportRow.total_prospects,
      };
      obj.push(newRow);
    })
    return obj;
  }

  getTimewiseStatsForProspectsStats(stats: Stats.ITimewiseReportStats[]) {

    return (_.map(stats, (stat) => {
      return (
        {
          total_prospects: stat.total_prospects,
          total_sent: stat.total_sent,
          total_clicked: stat.total_clicked,
          total_bounced: stat.total_bounced,
          total_opened: stat.total_opened,
          total_replied: stat.total_replied,
          total_opted_out: stat.total_opted_out,
          positive_response: stat.reply_sentiment_stats.positive,
          time_axis_name: stat.time_axis_name,
          time_axis_timestamp: stat.time_axis_timestamp
        }
      )
    }))
  }

  getCampaignSendStartColumns(): Campaigns.ITableColumn[] {
    return ([
      {
        name: 'Team Name',
        width: 3,
        field: 'team_name'
      },
      {
        name: 'Campaign Name',
        width: 3,
        field: 'campaign_name'
      },
      {
        name: 'Sent Prospects',
        width: 1,
        field: 'total_sent_prospects'
      },
      {
        name: 'Unsent Prospects',
        width: 1,
        field: 'total_unsent_prospects'
      },
      {
        name: 'Prospects In Block List',
        width: 1,
        field: 'total_unsent_prospects_in_dnc'
      },
      {
        name: 'Prospects In Same Timezone',
        width: 1,
        field: 'total_unsent_prospects_in_same_tz'
      },
      {
        name: 'Prospects With Invalid Emails',
        width: 1,
        field: 'total_unsent_prospects_with_invalid_emails'
      },
      {
        name: 'Prospects with Missing Merge Tags',
        width: 1,
        field: 'total_unsent_prospects_with_missing_merge_tags'
      },
      {
        name: 'Prospects with previous task not done',
        width: 1,
        field: 'total_unsent_prospects_with_previous_task_not_done'
      },
      {
        name: 'Valid Prospects (Likely)',
        width: 1,
        field: 'total_unsent_prospects_that_are_likely_valid'
      },
      {
        name: 'Analysis Result',
        width: 3,
        field: 'campaign_analysis_result'
      },
    ])
  }

  getNavSections() {
    const tabs = this.getAllTabs();
    const isAdmin = this.props.logInStore!.getIsTeamAdmin;
    const isCampaign = !!this.props.match.params.campaignId;
    // Helper to find tab by value
    const findTab = (value: string) => tabs.find(tab => tab.value === value);
    
    // For campaign-specific reports, filter sections based on campaign steps
    let channelsWithSteps: string[] = [];
    if (isCampaign) {
      const campaignSteps = this.props.campaignStore?.getStepVariants;
      channelsWithSteps = getChannelsFromSteps(campaignSteps);
    }
    
    let navSections: ISRVerticalNavSection[] = [];
    
    // Only include Email section if campaign has email steps or if not a campaign
    if (!isCampaign || channelsWithSteps.includes('email')) {
      const emailSection = {
        name: "Email",
        tabs: [
          'email_activity',
          'templates',
          'lists',
          'link_clicks',
          'best_time_to_send',
          sender_email_rotation,
          'emails_sent',
          'click_tracking',
          'reply_sentiment',
          'campaign_send_start',
          'chart_test',
        ].map(findTab).filter((tab): tab is ISRVerticalSectionNavItem => Boolean(tab)),
        icon: "sr_icon_mail"
      };
      navSections.push(emailSection);
    }
    
    // Only include Call section if campaign has call steps or if not a campaign
    if (!isCampaign || channelsWithSteps.includes('call')) {
      const callSection = {
        name: "Call",
        tabs: [
          'call_activity_report',
          'call_disposition_report',
        ].map(findTab).filter((tab): tab is ISRVerticalSectionNavItem => Boolean(tab)),
        icon: "sr_icon_phone"
      };
      navSections.push(callSection);
    }
    
    // Only include LinkedIn section if campaign has linkedin steps or if not a campaign
    if (!isCampaign || channelsWithSteps.includes('linkedin')) {
      const linkedinSection = {
        name: "Linkedin",
        tabs: [
          'linkedin_activity_report',
        ].map(findTab).filter((tab): tab is ISRVerticalSectionNavItem => Boolean(tab)),
        icon: "sr_icon_linkedin"
      };
      navSections.push(linkedinSection);
    }
    
    // Always include Prospect section
    const prospectSection = {
      name: "Prospect",
      tabs: [
        'prospect_category',
        'tags',
        'prospects',
        'hot_prospects',
      ].map(findTab).filter((tab): tab is ISRVerticalSectionNavItem => Boolean(tab)),
      icon: "sr_icon_prospects"
    };
    navSections.push(prospectSection);

    if(isAdmin && !isCampaign) {
      navSections.push({
        name: "Team Leaderboard",
        tabs: [
          'team',
        ].map(findTab).filter((tab): tab is ISRVerticalSectionNavItem => Boolean(tab)),
        icon: "sr_icon_users"
      })
    }

    return navSections;

  }

  render() {
    const logInStore = this.props.logInStore || {} as LogIn.ILogInStore;
    // const tabs = this.getAllTabs();
    const basePath = this.props.match.path;

    console.log('debug basePath', basePath);

    const loggedInAccountId = logInStore.accountInfo.internal_id;

    const teams = logInStore.accountInfo.teams;

    const currTeamId = logInStore.getCurrentTeamId;

    const currentTeamMembers = getCurrentTeamMembers({
      teams: teams,
      currTeamId: currTeamId,
    });

    const { reportsFilterObj } = this.state;
    const isCampaign = !!this.props.match.params.campaignId;

    const teamMemberOptions: {
      id: string | number;
      text: string;
    }[] = getTeamMemberOptions({
      loggedInAccountId: loggedInAccountId,
      teamMembers: currentTeamMembers,
    }).map((t) => ({ id: t.value, text: t.displayText }));

    const isLoading = this.state.isLoadingCampaignTags;
    // const loc = this.props.location.pathname;
    // const isBestTimeTab = _.includes(loc, '/besttime');
    // const isHotProspectsTab = _.includes(loc, '/hot_prospects');

    const permissions = logInStore.getTeamRolePermissions.permissions;

    const canDownloadReport = checkPermission(permissions.download_reports);
    const hotProspectsEnabled = logInStore.getFeatureFlagsObj.ff_reports_hot_prospect;
    const isAgencyAdminDashboardView = (logInStore.getCurrentTeamId === 0);

    return (
      <div className={'reports-page ' + (isCampaign ? 'local-page' : 'global-page')}>
        <Helmet>
          <title>Reports</title>
        </Helmet>

        <div className={isCampaign ? 'local-page-flex' : 'global-page-flex'}>





          {/* Downalod buttons */}
          {/* keeping this buttons here because the reports states are present in this file */}
          <span style={{ display: 'none' }}>
            <CSVLink data={this.state.templateReports} filename={this.state.downloadFileName} id='template_report_download' />
            <CSVLink data={this.state.listsReport} filename={this.state.downloadFileName} id='list_report_download' />
            <CSVLink data={this.state.callReport} filename={this.state.downloadFileName} id='call_activity_report' />
            <CSVLink data={this.state.linkedinReport} filename={this.state.downloadFileName} id='linkedin_activity_report_download' />
            <CSVLink data={this.state.linksReport} filename={this.state.downloadFileName} id='links_report_download' />
            <CSVLink data={this.state.teamsReport} filename={this.state.downloadFileName} id='teams_report_download' />
            <CSVLink data={this.getCategoryReportObjToDownload(this.state.prospectCategoryReport || [])} filename={this.state.downloadFileName} id='prospect_category_report_download' />
            <CSVLink data={this.getSenderEmailReportObjToDownload(this.state.senderEmailReport || [])} filename={this.state.downloadFileName} id='sender_email_report_download' />
            <CSVLink data={this.state.clickTrackingReport} filename={this.state.downloadFileName} id='click_tracking_report_download' />
            <CSVLink data={this.getTagReportObjToDownload(this.state.tagsReport || [])} filename={this.state.downloadFileName} id='tag_report_download' />
          </span>



          <div className='main-content-strip h-full has-filter-banner flex-1 overflow-auto pt-[10px] bg-white' style={{ paddingRight: 'inherit' }}>
            {!!getReportsFilterCount(this.state.reportsFilterObj || {} as Stats.ICustomStatsFilter) &&
              <div className='filter-banner'>
                <div className='filter-banner-content ui tiny purple label'>{getReportsFilterCount(this.state.reportsFilterObj || {} as Stats.ICustomStatsFilter)} filter(s) applied</div>
              </div>
            }
            <div className='flex h-full'>
              <div className='!w-[250px] !mt-0'>
                <SRVerticalSectionNavV2
                  sections={this.getNavSections()}
                  onClick={(value: string) => {
                    const item = _.find(this.getAllTabs(), (tab) => { return tab.value == value })
                    this.onClickTab(item!)
                  }}
                  navType='onclick'
                  navClassName='sr-side-menu'
                />
              </div>
              <div className='flex-1 px-4 h-full'>
                <Switch>
                    {(!isCampaign || (isCampaign && this.props.campaignStore?.getBasicInfo?.settings)) && (
                      <SRRedirect
                        exact
                        from={`${basePath}/`}
                        to={`${basePath}/${!isCampaign ? "overview" : getDefaultPage(this.props.campaignStore?.getStepVariants)}`}
                      />
                    )}
                    <Route exact path={`${basePath}/email_activity`} render={(props: any) => {

                    return (
                      <ProspectsStats
                        downloadReport={canDownloadReport ? this.downloadReport : undefined}
                        reportsFilterObj={reportsFilterObj}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        updateReportsFilterObj={this.updateReportsFilterObjState}
                        overall_stats={this.state.overall_stats}
                        timewise_stats={this.getTimewiseStatsForProspectsStats(this.state.timewise_stats)}
                        timewise_stats_interval={this.state.timewise_stats_interval}
                        stepwise_stats={this.state.stepwise_stats}
                        isLoading={this.state.isLoadingCampaignTags}
                        isLoadingOverall={this.state.isLoadingOverall}
                        isLoadingTimewise={this.state.isLoadingTimewise}
                        isLoadingStepwise={isCampaign ? this.state.isLoadingStepwise : false}
                        canDownload={canDownloadReport}
                        campaigns={this.state.campaignOptions}
                        {...props} />
                    );
                  }
                  }></Route>
                  <Route exact path={`${basePath}/prospects`} render={(props: any) => {

                    return (
                      <ProspectStatsNew
                        alertStore={this.props.alertStore}
                        logInStore={this.props.logInStore}
                        campaignStore={this.props.campaignStore}
                        campaigns={this.state.campaignOptions}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        {...props}
                      />
                    )
                  }}></Route>
                  <Route exact path={`${basePath}/overview`} render={(props: any) => {

                    return (
                      <OverviewReport
                        alertStore={this.props.alertStore}
                        logInStore={this.props.logInStore}
                        campaignStore={this.props.campaignStore}
                        reportsFilterObj={reportsFilterObj}
                        campaigns={this.state.campaignOptions}
                        updateReportsFilterObj={this.updateReportsFilterObjState}
                        {...props}
                      />
                    )
                  }}></Route>
                  <Route exact path={`${basePath}/overview`} render={(props: any) => {

                    return (
                      <OverviewReport
                        alertStore={this.props.alertStore}
                        logInStore={this.props.logInStore}
                        campaignStore={this.props.campaignStore}
                        campaigns={this.state.campaignOptions}
                        {...props}
                      />
                    )
                  }}></Route>

                  <Route exact path={`${basePath}/campaign_send_start`} render={(props: any) => {
                    return (
                      // <div>
                      //   <div className='mb-3'>
                      //     <div>
                      //       <span className='sr-h3'>Campaign Send Start report</span>
                      //     </div>
                      //   </div>

                      //   <div className="w-full border-t mb-4"></div>
                      //   <Table>
                      //     <Table.Header>
                      //       <Table.Row>
                      //         {
                      //           _.map(this.getCampaignSendStartColumns(), (column, index) => {
                      //             return (
                      //               <Table.HeaderCell
                      //                 width={column.width}
                      //                 key={index}
                      //                 className={'column_' + column.field + ' sort-check'}
                      //               >
                      //                 {column.name} {column.info && <Popup trigger={<Icon style={{ float: 'none' }} name='info circle' size='small' />}>{column.info}</Popup>}
                      //               </Table.HeaderCell>
                      //             );
                      //           })
                      //         }
                      //       </Table.Row>
                      //     </Table.Header>
                      //     <Table.Body>
                      //       {
                      //         _.map(this.state.campaignSendStartReport, (row: Stats.ICampaignSendStartReport, rowIndex) => {
                      //           console.log("row =>", row)
                      //           return (
                      //             <Table.Row key={rowIndex}>
                      //               {
                      //                 _.map(this.getCampaignSendStartColumns(), (column: Campaigns.ITableColumn) => {
                      //                   console.log("column =>", column)
                      //                   return (
                      //                     <Table.Cell key={rowIndex + '_' + column.field} className={'table_' + column.field}>
                      //                       {(row as any)[column.field]}
                      //                     </Table.Cell>
                      //                   );
                      //                 })
                      //               }
                      //             </Table.Row>
                      //           );
                      //         })
                      //       }
                      //     </Table.Body>
                      //   </Table>
                      // </div>
                      <CampaignsSendStartReportComponent
                        columns={this.getCampaignSendStartColumns()}
                        report={this.state.campaignSendStartReport}
                        isLoadingReport={this.state.isLoadingCampaignSendStartReport}
                        isAgencyAdminDashboardView={isAgencyAdminDashboardView}
                      />
                    );
                  }
                  }></Route>

                  {!isCampaign &&
                    <Route exact path={`${basePath}/emails_sent`} render={(props: any) => {
                      return (
                        <EmailsSentReport
                          downloadReport={canDownloadReport ? this.downloadReport : undefined}
                          reportsFilterObj={reportsFilterObj}
                          teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                          campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                          updateReportsFilterObj={this.updateReportsFilterObjState}
                          isLoading={this.state.isLoadingCampaignTags}
                          areFilterOptionsLoading={isLoading}
                          accountTimezone={logInStore.getAccountInfo.timezone}
                          campaigns={this.state.campaignOptions}
                          {...props} />
                      );
                    }
                    }></Route>
                  }

                  <Route path={`${basePath}/tasks`} render={(props: any) => {
                    return (
                      <TasksReport />
                    );
                  }
                  }></Route>

                  <Route path={`${basePath}/call_activity_report`} render={(props: any) => {
                    return (
                      <CallReport
                        campaigns={this.state.campaignOptions}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        downloadReport={canDownloadReport ? this.downloadCallReport : undefined}
                      // updateReportsFilterObj={function (reportsFilterObj: Stats.ICustomStatsFilter): void {
                      //   throw new Error('Function not implemented.');
                      // } }
                      // reportsFilterObj={undefined}
                      // updateTeamsReport={function (teamsReport: any): {} {
                      //   throw new Error('Function not implemented.');
                      // } }
                      // canDownload={false}
                      // toggleShowFilter={function () {
                      //   throw new Error('Function not implemented.');
                      // } }
                      />
                    );
                  }
                  }></Route>
                  <Route path={`${basePath}/call_disposition_report`} render={(props: any) => {
                    return (
                      <CallDispositionReport
                        campaigns={this.state.campaignOptions}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                      />
                    );
                  }
                  }></Route>

                  <Route path={`${basePath}/linkedin_activity_report`} render={(props: any) => {
                    return (
                      <div className='overflow-x-hidden'>
                      <LinkedinActivityReport
                        campaigns={this.state.campaignOptions}
                        isCampaign={isCampaign}
                        downloadReport={canDownloadReport ? this.downloadLinkedinReport : undefined}

                      /></div>
                    );
                  }
                  }></Route>

                  <Route exact path={`${basePath}/templates`} render={(props: any) => {
                    return (
                      <TemplatesStats
                        downloadReport={canDownloadReport ? this.downloadReport : undefined}
                        reportsFilterObj={reportsFilterObj}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        updateReportsFilterObj={this.updateReportsFilterObjState}
                        isLoadingTemplatesReport={this.state.isLoadingTemplatesReport}
                        templateReports={this.state.templateReports}
                        updateTemplatesReport={this.updateTemplatesReport}
                        canDownload={canDownloadReport}
                        campaigns={this.state.campaignOptions}
                        {...props} />
                    )
                  }
                  }></Route>

                  <Route exact path={`${basePath}/lists`} render={(props: any) => {
                    return (
                      <ListsStats
                        downloadReport={canDownloadReport ? this.downloadReport : undefined}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        updateReportsFilterObj={this.updateReportsFilterObjState}
                        reportsFilterObj={reportsFilterObj}
                        listsReport={this.state.listsReport}
                        isLoadingListsReport={this.state.isLoadingListsReport}
                        updateListsReport={this.updateListsReport}
                        canDownload={canDownloadReport}
                        campaigns={this.state.campaignOptions}
                        {...props} />
                    );
                  }
                  } ></Route>

                  <Route exact path={`${basePath}/links`} render={(props: any) => {
                    return (
                      <LinksStats
                        downloadReport={canDownloadReport ? this.downloadReport : undefined}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        updateReportsFilterObj={this.updateReportsFilterObjState}
                        reportsFilterObj={reportsFilterObj}
                        linksReport={this.state.linksReport}
                        isLoadingLinksReport={this.state.isLoadingLinksReport}
                        updateLinksReport={this.updateLinksReport}
                        canDownload={canDownloadReport}
                        campaigns={this.state.campaignOptions}
                        {...props} />
                    )
                  }
                  }></Route>

                  <Route exact path={`${basePath}/besttime`} render={(props: any) => {
                    return (
                      <BestTimeOfDayStats

                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        updateReportsFilterObj={this.updateReportsFilterObjState}
                        bestTimeFilterOptions={bestTimeFilterOptions}
                        reportsFilterObj={reportsFilterObj}
                        bestTimeReport={this.state.bestTimeReport}
                        isLoadingBestTimeReport={this.state.isLoadingBestTimeReport}
                        canDownload={canDownloadReport}
                        campaigns={this.state.campaignOptions}
                        {...props} />
                    );
                  }}></Route>

                  <Route exact path={`${basePath}/team`} render={(props: any) => {
                    return (
                      <TeamStats

                        downloadReport={canDownloadReport ? this.downloadReport : undefined}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        updateReportsFilterObj={this.updateReportsFilterObjState}

                        reportsFilterObj={reportsFilterObj}
                        teamsReport={this.state.teamsReport}
                        isLoadingTeamsReport={this.state.isLoadingTeamsReport}
                        updateTeamsReport={this.updateTeamsReport}
                        canDownload={canDownloadReport}
                        campaigns={this.state.campaignOptions}
                        {...props} />
                    )
                  }
                  }></Route>

                  <Route exact path={`${basePath}/prospect_categories`} render={(props: any) => {
                    return (
                      <CategoryStats

                        downloadReport={canDownloadReport ? this.downloadReport : undefined}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        updateReportsFilterObj={this.updateReportsFilterObjState}

                        reportsFilterObj={reportsFilterObj}
                        categoryReport={this.state.prospectCategoryReport}
                        isLoadingCategoryReport={this.state.isLoadingProspectCategoryReport}
                        updateProspectCategoryReport={this.updateProspectCategoryReport}
                        canDownload={canDownloadReport}
                        campaigns={this.state.campaignOptions}
                        {...props} />
                    )
                  }
                  }></Route>

                  <Route exact path={`${basePath}/${sender_email_rotation}`} render={(props: any) => {
                    return (
                      <SenderEmailStats

                        downloadReport={canDownloadReport ? this.downloadReport : undefined}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        updateReportsFilterObj={this.updateReportsFilterObjState}

                        reportsFilterObj={reportsFilterObj}
                        senderEmailReport={this.state.senderEmailReport}
                        isLoadingSenderEmailReport={this.state.isLoadingSenderEmailReport}
                        updateSenderEmailReport={this.updateSenderEmailReport}
                        canDownload={canDownloadReport}
                        campaigns={this.state.campaignOptions}
                        logInStore={logInStore}
                        {...props} />
                    )
                  }
                  }></Route>

                  <Route exact path={`${basePath}/tags`} render={(props: any) => {
                    return (
                      <TagStats

                        downloadReport={canDownloadReport ? this.downloadReport : undefined}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        updateReportsFilterObj={this.updateReportsFilterObjState}

                        reportsFilterObj={reportsFilterObj}
                        tagsReport={this.state.tagsReport}
                        isLoadingTagsReport={this.state.isLoadingTagsReport}
                        updateTagsReport={this.updateTagsReport}
                        canDownload={canDownloadReport}
                        campaigns={this.state.campaignOptions}
                        {...props} />
                    )
                  }
                  }></Route>

                  <Route exact path={`${basePath}/click_tracking`} render={(props: any) => {
                    return (
                      <ClickTrackingStats
                        downloadReport={canDownloadReport ? this.downloadReport : undefined}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        updateReportsFilterObj={this.updateReportsFilterObjState}

                        reportsFilterObj={reportsFilterObj}
                        clickTrackingReport={this.state.clickTrackingReport}
                        isLoadingClickTrackingReport={this.state.isLoadingClickTrackingReport}
                        updateClickTrackingReport={this.updateClickTrackingReport}
                        canDownload={canDownloadReport}
                        reportsPage={this.state.reportsPage}
                        searchPageSize={this.state.searchPageSize}
                        searchTotalCount={this.state.searchTotalCount}
                        searchMaxCount={this.state.searchMaxCount}
                        onPageChange={this.onPageChange}
                        campaigns={this.state.campaignOptions}
                        {...props} />
                    )
                  }
                  }></Route>

                  <Route exact path={`${basePath}/hot_prospects`} render={(props: any) => {
                    return (
                      <HotProspectsReports

                        reportsFilterObj={reportsFilterObj}
                        teamMemberOptions={!isCampaign ? teamMemberOptions : undefined}
                        campaignTagsOptions={!isCampaign && logInStore.accountInfo.org.org_metadata.show_campaign_tags ? this.getCampaignTagsOptions() : undefined}
                        updateReportsFilterObj={this.updateReportsFilterObjState}
                        isLoadingOptions={this.state.isLoadingCampaignTags}
                        campaignTags={this.getCampaignTagsOptionsWithDefaultAll()}
                        accountTimezone={this.props.logInStore?.getAccountInfo.timezone!}
                        showUpgradePrompt={!hotProspectsEnabled}
                        showCampaignTags={(!!logInStore.getAccountInfo.org.org_metadata.show_campaign_tags)}
                      />
                    );
                  }
                  }></Route>
                  <Route exact path={`${basePath}/reply_sentiment`} render={(props: any) => {
                    return (
                      <ReplySentimentStats
                        reportsFilterObj={reportsFilterObj}
                        replySentimentReport={this.state.replySentimentReport}
                        overallStats={this.state.overall_stats}
                        isLoadingReplySentimentReport={this.state.isLoadingReplySentimentReport}
                        campaigns={this.state.campaignOptions}
                        {...props} />
                    );
                  }
                  }></Route>
                  <Route exact path={`${basePath}/chart_test`} render={(props: any) => {
                    return <ChartTest {...props} />;
                  }}></Route>
                  <Route path="*" component={NotFoundPage} />

                </Switch>
              </div>
            </div>
          </div>

        </div>
      </div >
    )
  }
}


export const Reports = withRouter(inject('logInStore', 'campaignStore', 'alertStore', 'teamStore')(observer(ReportsComponent)));
