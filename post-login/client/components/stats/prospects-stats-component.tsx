import * as React from 'react';
import { inject, observer } from 'mobx-react';
// import * as campaignApi from '../../api/campaigns';
const rechart = require('recharts');
import * as _ from 'lodash';
import { reportColors } from '../../utils/colorUtils';
// const { ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend } = rechart;
const { <PERSON><PERSON><PERSON>, Pie, Cell } = rechart;
import * as moment from 'moment-timezone';
import { withRouter, RouteComponentProps } from 'react-router-dom';
// import { CONSTANTS } from '../../data/constants';
import { CommonReportsFilterComponent } from './common/common-report-filter-component';
import { SrSpinner } from '@sr/design-component';
import { ISRTableColSpan, ISRTableRow, SRTable } from '@sr/design-component';
import { LogIn, Campaigns } from '@sr/shared-product-components';
import StatsHeader from './common/stats-header';
import { ISrLineChartData } from '../../utils/sr-charts';
import { RenderChart } from './prospects-stats-new';
import { ISrBarChartData } from '../../utils/sr-charts';
import { Stats } from './stats';

interface ITimewiseStat {
  time_axis_timestamp: string;
  total_sent: number;
  total_bounced: number;
  total_opened: number;
  total_clicked: number;
  total_opted_out: number;
  total_replied: number;
  positive_response: number; 
}

export interface IGeneralStatsProps extends RouteComponentProps<any> {
  campaignStore?: Campaigns.ICampaignStore;
  alertStore?: Alerts.IAlertStore;
  logInStore?: LogIn.ILogInStore;
  reportsFilterObj: Stats.ICustomStatsFilter;
  downloadReport?: () => void
  teamMemberOptions?: { id: number | string, text: string }[]
  campaignTagsOptions?: { key: string, value: number, label: string }[]
  updateReportsFilterObj: (reportsFilterObj: Stats.ICustomStatsFilter) => void
  overall_stats: Stats.IStatsOverall;
  timewise_stats: ITimewiseStat[];
  timewise_stats_interval: "day" | "week";
  stepwise_stats: {}[],
  isLoading: boolean
  isLoadingOverall: boolean;
  isLoadingTimewise: boolean;
  isLoadingStepwise: boolean;

  canDownload: boolean;
  toggleShowFilter: () => void;
  campaigns: Stats.Options.ICampaignOptions
}

interface IGeneralStatsStates {
  // isDownloading?: boolean;
  hoverOnOverallStats: boolean;
  showFullCounts: boolean;
}

class ProspectsStatsComponent extends React.Component<IGeneralStatsProps, IGeneralStatsStates> {

  constructor(props: IGeneralStatsProps) {
    super(props);

    this.state = {
      // isDownloading: false,
      hoverOnOverallStats: false,
      showFullCounts: false
    }
    this.getColumns = this.getColumns.bind(this);
    this.formatXAxisTimeFormat = this.formatXAxisTimeFormat.bind(this);
    this.chartTooltipLableTimeFormat = this.chartTooltipLableTimeFormat.bind(this)
    this.clickedFormat = this.clickedFormat.bind(this);
    this.bouncedFormat = this.bouncedFormat.bind(this);
    this.positiveFormat = this.positiveFormat.bind(this);
    this.getTotalSentStepWise = this.getTotalSentStepWise.bind(this);
    this.getTotalSentTimeWise = this.getTotalSentTimeWise.bind(this);
    this.getTableRows = this.getTableRows.bind(this);
    this.countsOnMouseOver = this.countsOnMouseOver.bind(this);
    this.countsOnMouseLeave = this.countsOnMouseLeave.bind(this);
    // this.downloadReport = this.downloadReport.bind(this);
  }

  countsOnMouseOver() {
    this.setState({ showFullCounts: true })
  }

  countsOnMouseLeave() {
    this.setState({ showFullCounts: false })
  }


  getColumns() {
    const isDripCampaign =this.props.campaignStore?.getBasicInfo.settings?.campaign_type === "drip";
    
    const columns: Campaigns.ITableColumn[] = [
      {
        name: 'Email step',
        field: isDripCampaign ? 'display_node_id' : 'label',
        width: 2,
      },

      {
        name: 'Prospects contacted',
        field: 'total_sent',
        width: 1,
      },
      {
        name: 'Opened',
        field: 'total_opened',
        dataFormat: this.openFormat,
        dataSort: true,
        width: 1,
      },
      {
        name: 'Clicked',
        field: 'total_clicked',
        dataFormat: this.clickedFormat,
        dataSort: true,
        width: 1,
      },
      {
        name: 'Replied',
        field: 'total_replied',
        dataFormat: this.replyFormat,
        dataSort: true,
        width: 1,
      },
      {
        name: 'Positive',
        field: 'positive_response',
        dataFormat: this.positiveFormat,
        dataSort: true,
        width: 1,
      },
      {
        name: 'Unsubscribed',
        field: 'total_opted_out',
        dataFormat: this.optoutFormat,
        dataSort: true,
        width: 1,
      },
      {
        name: 'Bounced',
        field: 'total_bounced',
        dataFormat: this.bouncedFormat,
        dataSort: true,
        width: 1,
      },
    ];
    return columns;
  }

  openFormat(cell: any, row: any) {
    const open_rate = (row.total_sent) ? Math.ceil((row.total_opened / row.total_sent) * 100) : 0;
    return (
      <div>
        {open_rate}% ({row.total_opened})
      </div>
    );
  }

  clickedFormat(cell: any, row: any) {
    const clicked_rate = (row.total_sent) ? Math.ceil((row.total_clicked / row.total_sent) * 100) : 0;
    return (
      <div>
        {clicked_rate}% ({row.total_clicked})
      </div>
    );
  }

  replyFormat(cell: any, row: any) {
    const reply_rate = (row.total_sent) ? Math.ceil((row.total_replied / row.total_sent) * 100) : 0;
    return (
      <div>
        {reply_rate}% ({row.total_replied})
      </div>);
  }


  positiveFormat(cell: any, row: any) {
    const positive_rate = (row.total_sent) ? Math.ceil((row.reply_sentiment_stats.positive / row.total_sent) * 100) : 0;
    return (
      <div>
          <div>
            {positive_rate}% ({row.reply_sentiment_stats.positive})
          </div>
      </div>
    );
  }

  optoutFormat(cell: any, row: any) {
    const opted_out_rate = (row.total_sent) ? Math.ceil((row.total_opted_out / row.total_sent) * 100) : 0;
    return (
      <div>
        {opted_out_rate}% ({row.total_opted_out})
      </div>);
  }

  bouncedFormat(cell: any, row: any) {
    const total_bounced_rate = (row.total_sent) ? Math.ceil((row.total_bounced / row.total_sent) * 100) : 0;
    return (
      <div>
        {total_bounced_rate}% ({row.total_bounced})
      </div>);
  }

  getClassNameForFlexContainer(isCampaign: boolean) {
    return (isCampaign ? ('grid grid-cols-7 gap-8')  :'grid grid-cols-8 gap-8') + ' mt-6 mb-4'
  }

  /*
  downloadReport() {
    this.setState({ isDownloading: true });

    const reportsFilterObj = this.props.reportsFilterObj;
    const campaignIds = reportsFilterObj.campaign_ids;
    const from = reportsFilterObj.fromDay.getTime();
    const till = reportsFilterObj.tillDay.getTime();
    campaignApi.downloadCampaignReportV3(campaignIds, from, till).then((resp: any) => {
      this.setState({ isDownloading: false });
    }).catch((err: any) => {
      this.setState({ isDownloading: false });
    })
  }
  */

  getPieChart(piedata: any, COLORS: any) {
    const data: any = piedata || [];
    return (
      <PieChart widshowDateRangeModalth={60} height={80}>
        <Pie
          data={data}
          cx={30}
          cy={30}
          innerRadius={15}
          outerRadius={25}
          fill="#0088FE"
        >
          {
            data.map((entry: any, index: number) => <Cell key={index} fill={COLORS[index % COLORS.length]} />)
          }
        </Pie>
      </PieChart>
    );
  }

  getTotalSentTimeWise(timewise_stats: {}[]) {
    let totalSentTimeWiseIsZero: boolean = true;

    _.forEach(timewise_stats, (stat: any) => {

      // NOTE: in case of custom range if less than 3 days selected, we will add some empty object to make chart look good
      //  But that empty object does not have `total_sent` at field, so the below if condition was executed
      //  as `undefined !== 0` which is `true`
      //  now first we check if `total_sent` actually exist.
      if (stat.total_sent && stat.total_sent !== 0) {
        totalSentTimeWiseIsZero = false;
      }

      // return (stat.total_sent === 0);
    });
    return totalSentTimeWiseIsZero;
  }

  getTotalSentStepWise(stepwise_stats: {}[]) {
    let totalSentStepWiseIsZero: boolean = true;
    _.forEach(stepwise_stats, (stat: any) => {
      // console.log('stepwise stat', stat.total_sent);
      if (stat.total_sent !== 0) {
        totalSentStepWiseIsZero = false;
      }
      return (stat.total_sent === 0);
    })
    return totalSentStepWiseIsZero;
  }

  formatXAxisTimeFormat(tickItem: any) {
    const tz = this.props.logInStore!.getAccountInfo.timezone;

    if (tickItem === '') {

      // NOTE: in case of custom range if less than 3 days selected, we will add some empty object to make chart look good
      return '';

    } else if (this.props.timewise_stats_interval == "week") {

      const startDate = moment(tickItem).tz(tz).startOf('week');
      const endDate = moment(tickItem).tz(tz).endOf('week');

      return `${startDate.format('D')}-${endDate.format('D MMM')}`;

    } else {

      return moment(tickItem).tz(tz).format('D-MMM');
    }


  }


  chartTooltipLableTimeFormat(labelItem: any) {
    const tz = this.props.logInStore!.getAccountInfo.timezone;

    if (this.props.timewise_stats_interval == "week") {

      const startDate = moment(labelItem).tz(tz).startOf('week');
      const endDate = moment(labelItem).tz(tz).endOf('week');

      return `${startDate.format('D')}-${endDate.format('D MMM, Y')}`;

    } else {
      return moment(labelItem).tz(tz).format('D-MMM-Y');

    }


  }

  //set the text
  onMouseoverOverallStats(e: any) {
    this.setState({ hoverOnOverallStats: true })
  }

  //clear the text
  onMouseoutOverallStats(e: any) {
    this.setState({ hoverOnOverallStats: false })
  }


  getTableRows(stepwiseStats: {}[]): ISRTableRow[] {
    
    var rows: ISRTableRow[] = [];
    _.map(stepwiseStats, (row: any, rowIndex: any) => {
      const isABTestingEnabled = this.props.logInStore!.getFeatureFlagsObj.ff_ab_testing;
      const hasMoreThanOneVariant = row.variant_stats.length > 1;

      if (isABTestingEnabled && hasMoreThanOneVariant) {
        const rowsWithVariants = _.concat(row, row.variant_stats);
        return (
          _.map(rowsWithVariants, (rowWithVariant: any, index): ISRTableRow => {
            const classNameStr = (index > 0) ? 'variant-row' : '';
            return (
              {
                cells:
                  this.getColumns().map((column: any) => {
                    if (column.dataFormat) {
                      return {
                        cell: column.dataFormat('', rowWithVariant),
                        key: rowWithVariant.variant_id + '_' + column.field
                      }
                    } else if (column.field === 'display_node_id') {
                      const displayValue = rowWithVariant[column.field] || rowWithVariant['label'];
                      return {
                        cell: `${displayValue} ${rowWithVariant['variant_active'] === false ? '(Paused)' : ''}`,
                        key: rowWithVariant.variant_id + '_' + column.field,
                        className: 'label'
                      }
                    } else if (column.field === 'label') {
                      return {
                        cell: `${rowWithVariant[column.field]} ${rowWithVariant['variant_active'] === false ? '(Paused)' : ''}`,
                        key: rowWithVariant.variant_id + '_' + column.field,
                        className: 'label'
                      }
                    } else {
                      return {
                        cell: rowWithVariant[column.field],
                        key: rowWithVariant.variant_id + '_' + column.field
                      }
                    }
                  }),
                key: rowWithVariant.step_id + '_' + index,
                className: classNameStr
              }
            );
          })
        );
      } else {
        return (
          [{
            key: `${rowIndex}__noab`,
            cells: this.getColumns()
              .map((column: any, index: any) => {
                if (column.dataFormat) {
                  return {
                    cell: column.dataFormat('', row),
                    key: index + '_' + column.field
                  }
                } else if (column.field === 'display_node_id') {
                  const displayValue = `Step ${row[column.field]}` || row['label'];
                  return {
                    cell: displayValue,
                    key: index + '_' + column.field
                  }
                } else {
                  return {
                    cell: row[column.field],
                    key: index + '_' + column.field
                  }
                }
              })
          } as ISRTableRow]
        );
      }
    }).map(row => {
      rows = [...rows, ...row]
    })

    return rows;
  }

  render() {

    // NOTE: in case of custom range if less than 3 days selected, we will add some empty object to make chart look good
    // const emptyStatObject = {
    //   time_axis_timestamp: '',
    //   total_sent: 0,
    //   total_bounced: 0,
    //   total_opened: 0,
    //   total_clicked: 0,
    //   total_opted_out: 0,
    //   total_replied: 0,
    //   positive_response: 0
    // };
    const timewiseStatsFromApi = this.props.timewise_stats;
    // const timewise_stats = (timewiseStatsFromApi.length < 3 && timewiseStatsFromApi.length > 0) ? timewiseStatsFromApi.concat([emptyStatObject, emptyStatObject]) : timewiseStatsFromApi;
    const timewise_stats = timewiseStatsFromApi;

    console.log('timewise_stats', timewiseStatsFromApi);

    const chartDataBar : ISrBarChartData = {
      chart_type: 'bar_chart',
      report_type: 'emails_sent',
    report_label: 'Total emails sent over time (including follow ups)',
    y_axis: [
      { name: 'Sent', color: reportColors.sentColor, data: timewise_stats.map(stat => stat.total_sent) },
      { name: 'Bounced', color: reportColors.bouncedColor, data: timewise_stats.map(stat => stat.total_bounced) },
      { name: 'Opened', color: reportColors.openedColor, data: timewise_stats.map(stat => stat.total_opened) },
      { name: 'Clicked', color: reportColors.clickColor, data: timewise_stats.map(stat => stat.total_clicked) },
      { name: 'Unsubscribed', color: reportColors.unsubscribeColor, data: timewise_stats.map(stat => stat.total_opted_out) },
      { name: 'Replied', color: reportColors.repliedColor, data: timewise_stats.map(stat => stat.total_replied) },
      { name: 'Positive', color: reportColors.positiveColor, data: timewise_stats.map(stat => stat.positive_response ?? 0) }
    ],
    x_axis: timewise_stats.map(stat => stat.time_axis_timestamp)
  };

  const chartDataLine : ISrLineChartData = {
    chart_type: 'line_chart',
    report_type: 'emails_sent',
  report_label: 'Total emails sent over time (including follow ups)',
  y_axis: [
    { name: 'Sent', color: reportColors.sentColor, data: timewise_stats.map(stat => stat.total_sent) },
    { name: 'Bounced', color: reportColors.bouncedColor, data: timewise_stats.map(stat => stat.total_bounced) },
    { name: 'Opened', color: reportColors.openedColor, data: timewise_stats.map(stat => stat.total_opened) },
    { name: 'Clicked', color: reportColors.clickColor, data: timewise_stats.map(stat => stat.total_clicked) },
    { name: 'Unsubscribed', color: reportColors.unsubscribeColor, data: timewise_stats.map(stat => stat.total_opted_out) },
    { name: 'Replied', color: reportColors.repliedColor, data: timewise_stats.map(stat => stat.total_replied) },
    { name: 'Positive', color: reportColors.positiveColor, data: timewise_stats.map(stat => stat.positive_response ?? 0) }
  ],
  x_axis: timewise_stats.map(stat => stat.time_axis_timestamp)
};

    const reportsFilterObj = this.props.reportsFilterObj;

    const stepwiseStats = this.props.stepwise_stats || [];

    const {
      total_prospects = 0,
      total_sent = 0,
      total_clicked = 0,
      total_bounced = 0,
      total_opened = 0,
      total_replied = 0,
      total_opted_out = 0,
      reply_sentiment_stats = {
        positive: 0
      }
    } = (this.props.overall_stats || {}) as Stats.IStatsOverall;


    const durationInfo = (reportsFilterObj.time_period === 'week') ? 'Last week' : ((reportsFilterObj.time_period === 'month') ? 'Last month' : (moment(reportsFilterObj.fromDay).format('DD MMM YYYY') + ' to ' + moment(reportsFilterObj.tillDay).format('DD MMM YYYY')))

    // const hasCampaignId = (campaignIds.length > 0) ? true : false;

    //In campaign or when 1 campaign is selected
    const isCampaign = !!this.props.match.params.campaignId ? !!this.props.match.params.campaignId : true;

    const isCampaignTab = !!this.props.match.params.campaignId;

    const hoverOnOverallStats = this.state.hoverOnOverallStats;


    return (
      <div className='h-[inherit] flex flex-col' >

        {/* Campaign, time period, download report */}
        <StatsHeader
          heading="Email Activity"
          tooltipText="Prospects contacted, in the selected duration, with the opened, clicked, replied, unsubscribed and bounced data. Select a campaign to also see step-wise stats for the campaign."
        />

        <div className="w-full border-t mb-4"></div>
        <CommonReportsFilterComponent
          downloadReport={this.props.downloadReport}
          alertStore={this.props.alertStore}
          logInStore={this.props.logInStore}
          reportsFilterObj={this.props.reportsFilterObj}
          updateReportsFilterObj={this.props.updateReportsFilterObj}
          teamMemberOptions={this.props.teamMemberOptions}
          campaignTagsOptions={this.props.campaignTagsOptions}
          isLoading={this.props.isLoading}
          isCampaign={isCampaignTab}
          campaigns={this.props.campaigns}
        />


        {/* Stats */}
        <div className='row flex-1 mb-4'>
          <div className='sixteen wide column'>
            <div className='stats-charts relative'>

              {/* Overall stats */}
              {this.props.isLoadingOverall &&
                <SrSpinner spinnerTitle='loading...' />
              }
              <div className={(this.getClassNameForFlexContainer(isCampaign)) + (this.props.isLoadingOverall ? ' !absolute w-full top-0 blur-sm ' : '')}
                onMouseEnter={this.onMouseoverOverallStats.bind(this)}
                onMouseLeave={this.onMouseoutOverallStats.bind(this)}
              >
                {!isCampaign && <div className='mr-8'>

                  <div className='flex justify-center'>
                    <div>
                      <div className='text-center mb-3 sr-h4'>{total_prospects}</div>
                      <div className='text-sr-default-grey text-center font-normal'>Total Prospects</div>
                    </div>
                  </div>
                </div>

                }
                <div className='mr-8'>
                  <div className='flex justify-center'>
                    <div>
                      <div className='text-center mb-3 sr-h4'>{total_sent}</div>
                      <div className='text-sr-default-grey text-center font-normal'>Prospects Contacted</div>
                    </div>
                  </div>
                </div>
                <div className='mr-8'>
                  <div className='flex justify-center'>
                    <div>
                      <div className='text-center mb-3 sr-h4'>{hoverOnOverallStats
                        ? total_bounced
                        : `${total_sent ? Math.ceil((total_bounced / total_sent) * 100) : 0}%`
                      }</div>
                      <div className='text-sr-default-grey text-center font-normal'>Bounced</div>
                    </div>
                  </div>

                </div>
                <div className='mr-8'>
                  <div className='flex justify-center'>
                    <div>
                      <div className='text-center mb-3 sr-h4'>{hoverOnOverallStats
                        ? total_opened
                        : `${total_sent ? Math.ceil((total_opened / total_sent) * 100) : 0}%`
                      }</div>
                      <div className='text-sr-default-grey text-center font-normal'>Opened</div>
                    </div>
                  </div>
                </div>
                <div className='mr-8'>
                  <div className='flex justify-center'>
                    <div>
                      <div className='text-center mb-3 sr-h4'>{hoverOnOverallStats
                        ? total_clicked
                        : `${total_sent ? Math.ceil((total_clicked / total_sent) * 100) : 0}%`
                      }</div>
                      <div className='text-sr-default-grey text-center font-normal'>Clicked</div>
                    </div>
                  </div>

                </div>
                <div className='mr-8'>
                  <div className='flex justify-center'>
                    <div>
                      <div className='text-center mb-3 sr-h4'>{hoverOnOverallStats
                        ? total_opted_out
                        : `${total_sent ? Math.ceil((total_opted_out / total_sent) * 100) : 0}%`
                      }</div>
                      <div className='text-sr-default-grey text-center font-normal'>Unsubscribed</div>
                    </div>
                  </div>

                </div>
                <div className='mr-8'>
                  <div className='flex justify-center'>
                    <div>
                      <div className='text-center mb-3 sr-h4'>{hoverOnOverallStats
                        ? total_replied
                        : `${total_sent ? Math.ceil((total_replied / total_sent) * 100) : 0}%`
                      }</div>
                      <div className='text-sr-default-grey text-center font-normal'>Replied</div>
                    </div>
                  </div>
                </div>

                  <div className='mr-8'>
                    <div className='flex justify-center'>
                      <div>
                        <div className='text-center mb-3 sr-h4'>{hoverOnOverallStats
                          ? reply_sentiment_stats.positive
                          : `${total_sent ? Math.ceil((reply_sentiment_stats.positive / total_sent) * 100) : 0}%`
                        }</div>
                        <div className='text-sr-default-grey text-center font-normal'>Positive</div>
                      </div>
                    </div>

                  </div>
              </div>
              {/* Timewise stats */}
              <div className='relative w-full'>
                {this.props.isLoadingTimewise && <SrSpinner spinnerTitle='loading...' />}
                <div className={'sixteen wide column ' + (this.props.isLoadingTimewise ? '!absolute w-full top-0 blur' : (this.getTotalSentTimeWise(timewise_stats) ? 'blur' : ''))} >
                  <div>
                    {/* <ResponsiveContainer width='100%' height={300}>
                      {timewise_stats && timewise_stats.length > 60 ?
                        <LineChart width={500} height={300} data={timewise_stats}>
                          <XAxis dataKey='time_axis_timestamp' tickFormatter={this.formatXAxisTimeFormat} />
                          <YAxis />
                          <CartesianGrid stroke='#eee' strokeDasharray='5 5' />
                          <Tooltip labelFormatter={this.chartTooltipLableTimeFormat} />
                          <Legend />
                          <Line type='monotone' dataKey='total_sent' name='Sent' stroke={reportColors.sentColor} minPointSize={1} />
                          <Line type='monotone' dataKey='total_bounced' name='Bounced' stroke={reportColors.bouncedColor} minPointSize={1} />
                          <Line type='monotone' dataKey='total_opened' name='Opened' stroke={reportColors.openedColor} minPointSize={1} />
                          <Line type='monotone' dataKey='total_clicked' name='Clicked' stroke={reportColors.clickColor} minPointSize={1} />
                          <Line type='monotone' dataKey='total_opted_out' name='Unsubscribed' stroke={reportColors.unsubscribeColor} minPointSize={1} />
                          <Line type='monotone' dataKey='total_replied' name='Replied' stroke={reportColors.repliedColor} minPointSize={1} />

                        </LineChart> :
                        <BarChart width={500} height={300} data={timewise_stats}>
                          <XAxis dataKey='time_axis_timestamp' tickFormatter={this.formatXAxisTimeFormat} />
                          <YAxis />
                          <CartesianGrid stroke='#eee' strokeDasharray='5 5' />
                          <Tooltip labelFormatter={this.chartTooltipLableTimeFormat} />
                          <Legend />
                          <Bar stackId="a" maxBarSize={50} fillOpacity="0.9" dataKey='total_sent' name='Sent' fill={reportColors.sentColor} minPointSize={1} />
                          <Bar stackId="a" maxBarSize={50} fillOpacity="1" dataKey='total_bounced' name='Bounced' fill={reportColors.bouncedColor} minPointSize={1} />
                          <Bar stackId="a" maxBarSize={50} fillOpacity="1" dataKey='total_opened' name='Opened' fill={reportColors.openedColor} minPointSize={1} />
                          <Bar stackId="a" maxBarSize={50} fillOpacity="1" dataKey='total_clicked' name='Clicked' fill={reportColors.clickColor} minPointSize={1} />
                          <Bar stackId="a" maxBarSize={50} fillOpacity="0.9" dataKey='total_opted_out' name='Unsubscribed' fill={reportColors.unsubscribeColor} minPointSize={1} />
                          <Bar stackId="a" maxBarSize={50} fillOpacity="1" dataKey='total_replied' name='Replied' fill={reportColors.repliedColor} minPointSize={1} />

                        </BarChart>}
                    </ResponsiveContainer> */}


                    <RenderChart
                      countsOnMouseOver={this.countsOnMouseOver}
                      countsOnMouseLeave={this.countsOnMouseLeave}
                      showFullCounts={this.state.showFullCounts}
                      chart_data={timewise_stats.length > 60 ? chartDataLine : chartDataBar}
                      count={timewise_stats.map(stat => stat.total_sent).reduce((acc, curr) => acc + curr, 0)}
                      // changeInPercentage={0}
            />


                  </div>
                </div>
                {!(this.props.isLoadingOverall || this.props.isLoadingStepwise || this.props.isLoadingTimewise) && this.getTotalSentTimeWise(timewise_stats) &&

                  <div className='flex absolute z-2 inset-x-0 top-0 justify-center'>
                    <div>
                      <div className='flex align-center'><img className='mx-auto' width={300} height={300} src='https://d3r6z7ju6qyotm.cloudfront.net/assets/2023_jan-empty-stats.svg' /></div>
                      <div className='sr-h3 '>{'Looks like we don\'t have enough data to display stats.'}</div>
                    </div>

                  </div>
                }
              </div>

              {/* Stepwise stats */}

              {isCampaign &&
                <>
                  <hr className="my-4 border-gray-200" />

                  <div className='row relative'>
                    {this.props.isLoadingStepwise && <SrSpinner spinnerTitle='loading...' />}
                    <div className={'sixteen wide column mb-[16px] ' + (this.props.isLoadingStepwise ? 'blur' : (this.getTotalSentStepWise(stepwiseStats) ? 'blur' : ''))}>
                      <div className='mt-4 mb-2 sr-h4'>Step-wise stats for prospects contacted in the selected duration ({durationInfo})</div>
                      <SRTable
                        columns={
                          this.getColumns().map(
                            (column, index) => {
                              return {
                                colSpan: column.width as ISRTableColSpan,
                                cell: column.name,
                              }
                            }
                          )
                        }
                        rows={
                          this.getTableRows(stepwiseStats)
                        }
                      />
                    </div>
                  </div>
                </>
              }
            </div>


            {/* {this.state.showModal &&
                <DateRangeModal
                  heading='Select time period'
                  onClose={this.onCloseModal}
                  onSubmit={this.onSubmitDateRange}
                  modalLoading={this.state.isDownloading || false}
                  initialFromDate={reportsFilterObj.fromDay}
                  initialTillDate={reportsFilterObj.tillDay} />
              } */}
          </div>
        </div>
      </div>
    )
  }
}
export const ProspectsStats = withRouter(inject('campaignStore', 'alertStore', 'logInStore')(observer(ProspectsStatsComponent)));