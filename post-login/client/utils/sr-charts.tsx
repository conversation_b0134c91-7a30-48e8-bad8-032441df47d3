import { ApexOptions } from 'apexcharts'
import * as React from 'react'
import * as _ from 'lodash'
import ReactApex<PERSON><PERSON> from "react-apexcharts";
import format from 'date-fns/format';


export interface SrReportChartSeries {
  name: string,
  color: string,
  data: number[]
}

type ISrChartType = 'bar_chart' | 'line_chart' | 'progressbar_chart'

export type reportType = 'prospects_contacted' |
  'prospects_contacted_by_channel' |
  'prospect_replies' |
  'engagement_touchpoints' |
  'top_campaigns' |
  'top_team_members' |
  'top_prospect_lists' |
  'top_campaigns_by_prospect_replies' |
  'top_email_sending_accounts' |
  'top_linkedin_engagement_accounts' |
  'top_calling_agents' |
  'prospects_added' |
  'calls_connected' |
  'calls_dialed' |
  'call_tasks' |
  'total_call_duration' |
  'average_call_duration' |
  'linkedin_tasks' |
  'linkedin_connection_requests' |
  'linkedin_messages' |
  'linkedin_view_profile' |
  'linkedin_inmails' |
  'emails_sent'

interface ISrChartData {
  chart_type: ISrChartType,
  report_type: reportType,
  report_label: string,
}

interface ProgressbarData {
  label: string,
  value: number,
  value_in_percentage: number
}

export interface ISrBarChartData extends ISrChartData {
  chart_type: "bar_chart",
  report_type: reportType,
  report_label: string,
  y_axis: SrReportChartSeries[],
  x_axis: string[]
}

export interface ISrLineChartData extends ISrChartData {
  chart_type: "line_chart",
  report_type: reportType,
  report_label: string,
  y_axis: SrReportChartSeries[],
  x_axis: string[]
}

export interface ISrProgressbarData extends ISrChartData {
  chart_type: "progressbar_chart",
  report_type: reportType,
  report_label: string,
  data: ProgressbarData[]
}

export type ISrChartDataType = ISrBarChartData | ISrLineChartData | ISrProgressbarData

function getChartOptions(chart_data: ISrBarChartData | ISrLineChartData): ApexOptions {
  console.log("chart_data debug 12", chart_data);

  // Check if we have only one data point
  const isSingleDataPoint = chart_data.x_axis.length === 1;

  return ({
    chart: {
      height: 200,
      stacked: true,
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    colors: ["#6D43FF", "#0F69FA", "#FA0F69", "#72D824", "#FAB212", "#64646E"],
    responsive: [{
      breakpoint: 480,
      options: {
        legend: {
          position: 'bottom',
          offsetX: -10,
          offsetY: 0
        }
      }
    }],
    dataLabels: {
      enabled: false,
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: isSingleDataPoint ? '15%' : '70%',
        dataLabels: {
          total: {
            enabled: false,
            style: {
              fontSize: '13px',
              fontWeight: 900
            }
          }
        }
      },
    },
    tooltip: {
      enabled: true,
      intersect: false,
      shared: true,
      x: {  //18-nov-24: It was calculating wrong date month for tooltip so added it explicitly
        formatter: function(val: number) {
          const date = new Date(val);
          return format(date, 'dd MMM')
        }
      }
    },
    xaxis: {
      type: 'datetime',
      categories: chart_data.x_axis,
      tickPlacement: 'on',
      labels: {
        format: "dd MMM",
        datetimeUTC: false,  // to not convert local DateTime to UTC
        // For single data point, limit the number of ticks to prevent repetition
        ...(isSingleDataPoint && {
          maxHeight: 50,
          hideOverlappingLabels: true
        })
      },
      // For single data point, add some padding and control tick generation
      ...(isSingleDataPoint && chart_data.x_axis[0] && {
        min: new Date(chart_data.x_axis[0]).getTime() - (12 * 60 * 60 * 1000), // 12 hours before
        max: new Date(chart_data.x_axis[0]).getTime() + (12 * 60 * 60 * 1000), // 12 hours after
        tickAmount: 1, // Force only one tick for single data point
        forceNiceScale: false
      })
    },
    legend: {
      position: 'bottom',
      showForSingleSeries: true
    },
    fill: {
      opacity: 1
    }
  })
}

interface IChartProps {
  chart_data: ISrChartDataType
}

const NoDataDisplay = () => {
  return (
    <div className='flex flex-col justify-center h-[276px] text-center font-sourcesanspro'>
      <div className='flex justify-center'>
        <svg width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.25 24C1.25 11.1594 11.6594 0.75 24.5 0.75C37.3406 0.75 47.75 11.1594 47.75 24C47.75 36.8406 37.3406 47.25 24.5 47.25C11.6594 47.25 1.25 36.8406 1.25 24Z" fill="white" stroke="#94A1B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <g clip-path="url(#clip0_17684_13897)">
            <path d="M16 25C16 24.7348 16.1054 24.4804 16.2929 24.2929C16.4804 24.1054 16.7348 24 17 24H21C21.2652 24 21.5196 24.1054 21.7071 24.2929C21.8946 24.4804 22 24.7348 22 25V31C22 31.2652 21.8946 31.5196 21.7071 31.7071C21.5196 31.8946 21.2652 32 21 32H17C16.7348 32 16.4804 31.8946 16.2929 31.7071C16.1054 31.5196 16 31.2652 16 31V25Z" stroke="#94A1B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M22 21C22 20.7348 22.1054 20.4804 22.2929 20.2929C22.4804 20.1054 22.7348 20 23 20H27C27.2652 20 27.5196 20.1054 27.7071 20.2929C27.8946 20.4804 28 20.7348 28 21V31C28 31.2652 27.8946 31.5196 27.7071 31.7071C27.5196 31.8946 27.2652 32 27 32H23C22.7348 32 22.4804 31.8946 22.2929 31.7071C22.1054 31.5196 22 31.2652 22 31V21Z" stroke="#94A1B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M28 17C28 16.7348 28.1054 16.4804 28.2929 16.2929C28.4804 16.1054 28.7348 16 29 16H33C33.2652 16 33.5196 16.1054 33.7071 16.2929C33.8946 16.4804 34 16.7348 34 17V31C34 31.2652 33.8946 31.5196 33.7071 31.7071C33.5196 31.8946 33.2652 32 33 32H29C28.7348 32 28.4804 31.8946 28.2929 31.7071C28.1054 31.5196 28 31.2652 28 31V17Z" stroke="#94A1B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M17 32H31" stroke="#94A1B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          </g>
          <defs>
            <clipPath id="clip0_17684_13897">
              <rect width="24" height="24" fill="white" transform="translate(13 12)" />
            </clipPath>
          </defs>
        </svg>
      </div>
      <div className='text-lg font-semibold text-sr-grey-100 my-[4px]'>No Data to Display</div>
      <div className='text-sr-grey-90 font-normal text-base'>To see data here, try adjusting your report filters</div>
    </div>
  )
}

export const SrChart = (props: IChartProps) => {
  if (
    (
      (props.chart_data.chart_type === 'bar_chart' ||
        props.chart_data.chart_type === 'line_chart') &&
      _.sum(props.chart_data.y_axis.map(series => {
        return (_.sum(series.data))
      })) === 0
    ) || (
      (props.chart_data.chart_type === 'progressbar_chart' &&
        _.sum(props.chart_data.data) === 0
      )
    )
  ) {
    return <NoDataDisplay />
  }
  else {
    if (props.chart_data.chart_type === 'bar_chart') {
      return (
        <ReactApexChart
          options={getChartOptions(props.chart_data)}
          type='bar'
          series={props.chart_data.y_axis}
          height={300}
        />
      )
    }
    else if (props.chart_data.chart_type === 'line_chart') {
      return (
        <ReactApexChart
          options={getChartOptions(props.chart_data)}
          type='line'
          series={props.chart_data.y_axis}
          height={300}
        />
      )
    }
    else if (props.chart_data.chart_type === 'progressbar_chart') {
      return (
        <>
          {props.chart_data.data.map(labelAndValue => {
            return (
              <div className='mb-[10px]'>
                <div className='flex justify-between'>
                  <p>{labelAndValue.label}</p>
                  <p>{labelAndValue.value}</p>
                </div>
                <div className="h-[2px] w-full bg-gray-200 rounded-full">
                  <div className="bg-blue-600 h-[2px] rounded-full" style={{ width: `${labelAndValue.value_in_percentage}%` }}></div>
                </div>
              </div>
            )
          })}
        </>
      )
    }
    else {
      return (<></>)
    }
  }

}
